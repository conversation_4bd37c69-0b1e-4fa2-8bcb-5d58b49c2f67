
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <title>Clients - Gestion des interventions</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <!-- Bootstrap + Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />

    <style>
        body {
            background-color: #f5f6fa;
            margin: 0;
            padding: 0;
        }

        #sidebar {
            width: 260px;
            background-color: #0d6efd;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 2rem;
            box-shadow: 3px 0 10px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            user-select: none;
            overflow-y: auto;
        }

            #sidebar .nav-link {
                color: #cbd5f0;
                font-weight: 600;
                padding: 15px 25px;
                border-left: 4px solid transparent;
                transition: background-color 0.3s, border-color 0.3s;
            }

                #sidebar .nav-link:hover {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

                #sidebar .nav-link.active {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

            #sidebar .submenu {
                padding-left: 1.5rem;
                font-weight: 500;
                display: none;
                flex-direction: column;
            }

                #sidebar .submenu .nav-link {
                    padding: 10px 25px;
                    font-size: 0.9rem;
                    color: #a8bbea;
                    border-left: none;
                }

                    #sidebar .submenu .nav-link:hover {
                        color: #d0e1ff;
                        background-color: transparent;
                    }

        .main-content {
            margin-left: 260px;
            padding: 40px 20px;
        }

        .client-photo {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 50%;
        }

        .client-table th, .client-table td {
            vertical-align: middle;
            text-align: center;
        }

        .btn-action {
            margin: 0 2px;
        }

        .add-client-btn {
            margin-top: 30px;
            padding: 10px 30px;
            font-size: 18px;
        }

        .card {
            border-radius: 16px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        @media (max-width: 768px) {
            #sidebar {
                position: static;
                width: 100%;
                height: auto;
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>

    <!-- 🔹 Sidebar -->
    <nav id="sidebar">
        <div class="text-center mb-4">
            <img src="images/Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white; margin-bottom: 10px;">
            <h4 style="margin: 0; font-weight: 700;">TechInterv</h4>
            <small>Logiciel d'interventions</small>
        </div>

        <a href="dashboard.html" class="nav-link">Accueil</a>
        <a href="clients.html" class="nav-link active">Clients</a>
        <a href="Gestion de stock.html" class="nav-link">Gestion de stock</a>
        <a href="Mission.html" class="nav-link">Mission</a>
        <a href="MonCompte.html" class="nav-link">Mon compte</a>

        <!-- Sous-menu réduit uniquement à "Déconnexion" -->
        <div class="submenu">
            <a href="login.html" class="nav-link text-danger">
                <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
            </a>
        </div>

        <a href="Sites.html" class="nav-link">Sites</a>
        <a href="Rapport.html" class="nav-link">Rapport</a>
    </nav>

    <!-- 🔸 Main -->
    <div class="main-content">
        <div class="text-center mb-5">
            <h2 class="fw-bold text-primary">Liste des clients</h2>

            <p class="text-muted">Gérez vos clients via ce tableau</p>
        </div>

        <div class="card p-4">
            <div class="table-responsive">
                <table class="table table-hover align-middle client-table">
                    <thead class="table-light">
                        <tr>
                            <th>Photo</th>
                            <th>Nom</th>
                            <th>Prénom</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><img src="images/client1.jpg" alt="Client" class="client-photo"></td>
                            <td>Lemoine</td>
                            <td>Sarah</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/client2.jpg" alt="Client" class="client-photo"></td>
                            <td>Dupont</td>
                            <td>Marc</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/client3.jpg" alt="Client" class="client-photo"></td>
                            <td>Benali</td>
                            <td>Youssef</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/client4.jpg" alt="Client" class="client-photo"></td>
                            <td>Mehdi</td>
                            <td>Karim</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/client5.jpg" alt="Client" class="client-photo"></td>
                            <td>Martin</td>
                            <td>Claire</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/client6.jpg" alt="Client" class="client-photo"></td>
                            <td>Saidi</td>
                            <td>Rania</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/client7.jpg" alt="Client" class="client-photo"></td>
                            <td>Tahiri</td>
                            <td>Walid</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/client8.jpg" alt="Client" class="client-photo"></td>
                            <td>Roche</td>
                            <td>Thomas</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/client9.jpg" alt="Client" class="client-photo"></td>
                            <td>Issa</td>
                            <td>Linda</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="images/client10.jpg" alt="Client" class="client-photo"></td>
                            <td>Hamzaoui</td>
                            <td>Sami</td>
                            <td>
                                <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                                <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                                <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                            </td>
                        </tr>
                    </tbody>

                </table>
            </div>

            <!-- ➕ Ajouter un client -->
            <div class="text-center">
                <button class="btn btn-success add-client-btn">
                    <i class="bi bi-plus-circle me-2"></i>Ajouter un client
                </button>
            </div>
        </div>
    </div>

    <!-- JS Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Toggle Submenu -->
    <script>
        document.getElementById('accountToggle').addEventListener('click', function () {
            var submenu = document.getElementById('submenuAccount');
            submenu.style.display = submenu.style.display === 'flex' ? 'none' : 'flex';
        });
    </script>

    <footer class="text-center mt-5">
        &copy; 2025 MonDashboard - Logiciel de gestion technique
    </footer>
</body>
</html>
