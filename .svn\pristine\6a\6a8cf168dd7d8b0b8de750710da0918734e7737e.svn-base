<!--<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Accueil - Logiciel de Gestion des Interventions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        body {
            background-color: #f8f9fa;
        }

        .hero-section {
            background-color: #e3f2fd;
            padding: 4rem 2rem;
            text-align: center;
        }

            .hero-section img {
                max-width: 400px;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }

        .btn-primary {
            background-color: #0d6efd;
        }

        .card img {
            max-height: 120px;
            object-fit: contain;
        }

        .illustration-ai {
            background-color: #ffffff;
            padding: 2rem 1rem;
            text-align: center;
        }

            .illustration-ai img {
                max-width: 600px;
                border-radius: 16px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            }
    </style>
</head>
<body>-->
<!-- 🌐 Navbar -->
<!--<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand" href="#">🧰 MonDashboard</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="mainNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item"><a class="nav-link active" href="#">Accueil</a></li>
                <li class="nav-item"><a class="nav-link" href="interventions.html">Interventions</a></li>
                <li class="nav-item"><a class="nav-link" href="tickets.html">Tickets</a></li>
                <li class="nav-item"><a class="nav-link" href="equipes.html">Équipes</a></li>
            </ul>
        </div>
    </div>
</nav>-->
<!-- 🏠 Hero Section -->
<!--<section class="hero-section">
    <div class="container">
        <h1 class="mb-3">Bienvenue dans votre logiciel de gestion des interventions</h1>
        <p class="lead mb-4">Gérez facilement vos techniciens, vos sites, vos tickets et vos missions avec une interface moderne et rapide.</p>
        <img src="images\Engineer.png" alt="Technicien IA" class="img-fluid" />

        <br /><br />
        <a href="interventions.html" class="btn btn-primary btn-lg">Accéder à la Gestion des Interventions</a>
    </div>
</section>-->
<!-- 🧠 Illustration IA réaliste -->
<!--<section class="illustration-ai">
    <div class="container">
        <h2 class="mb-4">Une interface moderne, claire et pensée pour les techniciens</h2>
        <img src="images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png" alt="Illustration IA réaliste" />

        <p class="mt-3">Votre tableau de bord met en avant l’essentiel pour piloter vos interventions, où que vous soyez.</p>
    </div>
</section>-->
<!-- 🧩 Sections Fonctionnalités -->
<!--<div class="container py-5">
    <div class="row text-center">
        <div class="col-md-4">
            <div class="card p-3">
                <img src="https://cdn-icons-png.flaticon.com/512/8836/8836119.png" alt="Gestion interventions" class="mb-3" />
                <h5>Gestion des Interventions</h5>
                <p>Assignez facilement des techniciens à des interventions selon leur spécialité et localisation.</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card p-3">
                <img src="https://cdn-icons-png.flaticon.com/512/726/726448.png" alt="Tickets clients" class="mb-3" />
                <h5>Suivi des Tickets</h5>
                <p>Créez, visualisez et mettez à jour les tickets des clients. Suivi complet de l’avancement.</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card p-3">
                <img src="https://cdn-icons-png.flaticon.com/512/7064/7064437.png" alt="Équipe" class="mb-3" />
                <h5>Gestion des Équipes</h5>
                <p>Organisez vos équipes, ajoutez de nouveaux agents et affectez-les aux missions rapidement.</p>
            </div>
        </div>
    </div>
</div>-->
<!-- 📞 Footer -->
<!--<footer class="bg-primary text-white text-center p-3 mt-5">
        &copy; 2025 MonDashboard - Logiciel de gestion technique
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>-->














<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Dashboard - Logiciel de Gestion des Interventions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            display: flex;
            flex-direction: column;
            /*overflow: hidden;*/ /* éviter scroll vertical sur la page */
        }

        #sidebar {
            width: 260px;
            background-color: #0d6efd; /* bleu bootstrap standard, plus clair */
            color: white;
            height: 100vh; /* pleine hauteur */
            position: fixed;
            padding-top: 2rem;
            box-shadow: 3px 0 10px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            user-select: none;
            overflow-y: auto; /* scroll si sidebar trop longue */
        }



            #sidebar .nav-link {
                color: #cbd5f0; /* bleu clair */
                font-weight: 600;
                padding: 15px 25px;
                border-left: 4px solid transparent;
                transition: background-color 0.3s, border-color 0.3s;
                cursor: pointer;
            }

                #sidebar .nav-link:hover {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

                #sidebar .nav-link.active {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

            #sidebar .submenu {
                padding-left: 1.5rem;
                font-weight: 500;
                display: none;
                flex-direction: column;
            }

                #sidebar .submenu .nav-link {
                    padding: 10px 25px;
                    font-size: 0.9rem;
                    color: #a8bbea;
                    border-left: none;
                }

                    #sidebar .submenu .nav-link:hover {
                        color: #d0e1ff;
                        background-color: transparent;
                    }

        /* Content */
        #content {
            margin-left: 260px;
            padding: 2rem 3rem;
            flex-grow: 1;
            height: 100vh; /* 👈 Ajoute cette ligne */
            overflow-y: auto;
            background-color: #f8f9fa;
        }
        /* Header */
        header {
            margin-bottom: 2.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

            header h1 {
                font-weight: 700;
                color: #0b3d91;
                font-size: 2.2rem;
                margin: 0;
            }

        /* Section Titles */
        h2.section-title {
            font-weight: 700;
            color: #0b3d91;
            margin-bottom: 1.5rem;
            border-bottom: 3px solid #0d6efd;
            padding-bottom: 0.5rem;
            max-width: fit-content;
        }

        /* Services we offer */
        .services {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.8rem;
            margin-bottom: 3rem;
        }

        .service-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgb(0 0 0 / 0.1);
            padding: 1.8rem 1.5rem;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            transition: box-shadow 0.3s ease;
        }

            .service-card:hover {
                box-shadow: 0 12px 30px rgb(0 0 0 / 0.15);
            }

        .service-image {
            height: 140px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 1rem;
            background-color: #e9ecef;
        }

        .service-title {
            font-weight: 700;
            font-size: 1.2rem;
            color: #0b3d91;
            margin-bottom: 0.8rem;
        }

        .service-desc {
            color: #444;
            font-size: 0.95rem;
            line-height: 1.4;
            flex-grow: 1;
        }

       

        /* Footer */
        footer {
            text-align: center;
            padding: 1.5rem 0;
            color: #777;
            font-size: 0.9rem;
            border-top: 1px solid #ddd;
            margin-top: 3rem;
        }

        /* Responsive tweaks */
        @media (max-width: 768px) {
            #content {
                padding: 1.5rem 1.5rem;
            }

            .why-choose {
                flex-direction: column;
            }
            .logout-link {
                font-weight: 600;
                font-size: 0.95rem;
                padding: 10px 15px;
                border-radius: 12px;
                transition: all 0.3s ease;
                color: #343a40; /* Gris foncé pro */
                text-decoration: none;
            }

                .logout-link:hover {
                    background-color: #f1f1f1;
                    text-decoration: none;
                    color: #000;
                    box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
                }

        }
    </style>
</head>
<body>

    <!-- Sidebar -->
    <nav id="sidebar" class="d-flex flex-column">
        <!-- En-tête de la sidebar -->
        <div class="text-center mb-4">
            <img src="images/Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white; margin-bottom: 10px;">
            <h4 style="margin: 0; font-weight: 700;">TechInterv</h4>
            <small>Logiciel d'interventions</small>
        </div>

        <a href="Dashboard.html" class="nav-link active">Accueil</a>
        <a href="Clients.html" class="nav-link">Clients</a>
        <a href="Gestion de stock.html" class="nav-link">Gestion de stock</a>
        <a href="Mission.html" class="nav-link">Mission</a>
        <a href="MonCompte.html" class="nav-link">Mon compte</a>

        <!-- Sous-menu réduit uniquement à "Déconnexion" -->
        <div class="submenu">
            <a href="login.html" class="nav-link text-danger">
                <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
            </a>
        </div>
        <a href="Sites.html" class="nav-link">Sites</a>
        <a href="Rapport.html" class="nav-link">Rapport</a>

        <a href="Login.html" onclick="logout()" class="nav-link logout-link mt-auto d-flex align-items-center gap-2">
            <i class="bi bi-box-arrow-right"></i>
            <span>Se d&#233;connecter</span>
        </a>

    </nav>

    <!-- Main content -->
    <main id="content">
        <header>
            <h1>Bienvenue dans votre logiciel de gestion</h1>
        </header>

        <!-- Services we offer -->
        <!-- 🌟 Section Services -->
        <!-- 🌟 Section Services -->
        <section class="py-5 bg-light">
            <div class="container">
                <h2 class="text-center mb-5 fw-bold text-primary">Nos services</h2>

                <div class="row g-4">

                    <!-- 🔧 Gestion des interventions -->
                    <div class="col-md-4">
                        <div class="card service-card h-100 text-center p-3 border-0 shadow-sm">
                            <img src="images/Engineer.png" alt="Intervention" class="service-img mx-auto mb-3" />
                            <h5 class="fw-semibold">Gestion des interventions</h5>
                            <p>Assignez vos techniciens, g&#233;rez les sites et suivez vos missions sur une seule plateforme.</p>
                        </div>
                    </div>

                    <!-- 💬 Support client -->
                    <div class="col-md-4">
                        <div class="card service-card h-100 text-center p-3 border-0 shadow-sm">
                            <img src="images/man-8106958_1280-removebg-preview.png" alt="Support client" class="service-img mx-auto mb-3" />
                            <h5 class="fw-semibold">Support client</h5>
                            <p>Un service d&#39;assistance r&#233;actif pour r&#233;pondre aux besoins de vos clients.</p>
                        </div>
                    </div>

                    <!-- 📊 Analyse avancée -->
                    <div class="col-md-4">
                        <div class="card service-card h-100 text-center p-3 border-0 shadow-sm">
                            <img src="images/investigation-9604083_1280-removebg-preview.png" alt="Analyse" class="service-img mx-auto mb-3" />
                            <h5 class="fw-semibold">Analyse avanc&#233;e</h5>
                            <p>Des indicateurs clairs pour piloter vos performances et am&#233;liorer votre efficacit&#233;.</p>
                        </div>
                    </div>

                    <!-- 🗓️ Planification -->
                    <div class="col-md-4">
                        <div class="card service-card h-100 text-center p-3 border-0 shadow-sm">
                            <img src="images/brain-1710293_1280.png" alt="Planification" class="service-img mx-auto mb-3" />
                            <h5 class="fw-semibold">Planification intelligente</h5>
                            <p>Cr&#233;ez automatiquement les plannings de vos &#233;quipes en fonction des priorit&#233;s et des disponibilit&#233;s.</p>
                        </div>
                    </div>

                    <!-- 📍 Suivi en temps réel -->
                    <div class="col-md-4">
                        <div class="card service-card h-100 text-center p-3 border-0 shadow-sm">
                            <img src="images/arrow-303116_1280.png" alt="Tracking" class="service-img mx-auto mb-3" />
                            <h5 class="fw-semibold">Suivi en temps r&#233;el</h5>
                            <p>Visualisez en direct la position et l&#39;avancement des missions de chaque technicien.</p>
                        </div>
                    </div>

                    <!-- 📑 Rapports -->
                    <div class="col-md-4">
                        <div class="card service-card h-100 text-center p-3 border-0 shadow-sm">
                            <img src="images/information-275708_1280.png" alt="Rapports" class="service-img mx-auto mb-3" />
                            <h5 class="fw-semibold">Rapports &amp; Statistiques</h5>
                            <p>Exportez vos donn&#233;es et obtenez des rapports visuels en un clic.</p>
                        </div>
                    </div>

                </div>
            </div>
        </section>
        <section class="mb-5">
            <h2 class="section-title">Statistiques du jour</h2>
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary shadow-sm text-center p-3">
                        <h5 class="mb-0">&#128295; Interventions</h5>
                        <h2 class="fw-bold">12</h2>
                        <p class="mb-0">Aujourd&#39;hui</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success shadow-sm text-center p-3">
                        <h5 class="mb-0">&#128104;&#8205;&#128295; Techniciens</h5>
                        <h2 class="fw-bold">5</h2>
                        <p class="mb-0">Actifs</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning shadow-sm text-center p-3">
                        <h5 class="mb-0">&#128205; Sites</h5>
                        <h2 class="fw-bold">8</h2>
                        <p class="mb-0">Intervention pr&#233;vue</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-danger shadow-sm text-center p-3">
                        <h5 class="mb-0">&#128680; Tickets urgents</h5>
                        <h2 class="fw-bold">3</h2>
                        <p class="mb-0">&#192; traiter</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="mb-5">
            <h2 class="section-title">&#128197; Prochaines missions</h2>
            <ul class="list-group shadow-sm">
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    Installation cam&#233;ra - <strong>Usine Est</strong>
                    <span class="badge bg-info">Aujourd&#39;hui 14h</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    Maintenance - <strong>Clinique Sant&#233; Plus</strong>
                    <span class="badge bg-secondary">Demain 9h</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    Diagnostic - <strong>Bureau Central</strong>
                    <span class="badge bg-warning text-dark">Jeu. 11h</span>
                </li>
            </ul>
        </section>

        <section class="mb-5">
            <h2 class="section-title">&#127915; Derniers tickets</h2>
            <div class="table-responsive shadow-sm">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>Description</th>
                            <th>Client</th>
                            <th>Date</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>#1023</td>
                            <td>Probl&#232;me d&#39;alarme</td>
                            <td>R&#233;sidence Atlas</td>
                            <td>29/07/2025</td>
                            <td><span class="badge bg-warning text-dark">En attente</span></td>
                        </tr>
                        <tr>
                            <td>#1022</td>
                            <td>Cam&#233;ra d&#233;fectueuse</td>
                            <td>Usine Nord</td>
                            <td>28/07/2025</td>
                            <td><span class="badge bg-success">R&#233;solu</span></td>
                        </tr>
                        <tr>
                            <td>#1021</td>
                            <td>Maintenance annuelle</td>
                            <td>Clinique Sant&#233; Plus</td>
                            <td>27/07/2025</td>
                            <td><span class="badge bg-info">Planifi&#233;</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section class="mb-5">
            <h2 class="section-title">&#128200; Performance des missions</h2>
            <div class="card shadow-sm p-4">
                <canvas id="missionsChart" height="120"></canvas>
            </div>
        </section>

        <!-- Chart.js -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

        <script>
            const ctx = document.getElementById('missionsChart').getContext('2d');
            const missionsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                    datasets: [{
                        label: 'Missions terminées',
                        data: [2, 4, 3, 6, 5, 3, 7],
                        backgroundColor: 'rgba(13, 110, 253, 0.2)',
                        borderColor: 'rgba(13, 110, 253, 1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#0d6efd',
                        pointBorderColor: '#fff',
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            backgroundColor: '#0d6efd',
                            titleColor: '#fff',
                            bodyColor: '#fff'
                        }
                    }
                }
            });
        </script>

        <style>
            /* Image des services */
            .service-img {
                width: 100px;
                height: auto;
                transition: transform 0.3s ease-in-out;
            }

            /* Zoom image au survol */
            .service-card:hover .service-img {
                transform: scale(1.1);
            }

            /* Animation légère de la carte */
            .service-card {
                background: #fff;
                border-radius: 15px;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

                /* Élément qui flotte au survol */
                .service-card:hover {
                    transform: translateY(-6px);
                    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
                }
        </style>



        <footer>
            &copy; 2025 MonDashboard - Logiciel de gestion technique
        </footer>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const accountToggle = document.getElementById('accountToggle');
        const submenuAccount = document.getElementById('submenuAccount');
        accountToggle.addEventListener('click', () => {
            if (submenuAccount.style.display === 'flex') {
                submenuAccount.style.display = 'none';
                accountToggle.textContent = 'Mon compte ▼';
            } else {
                submenuAccount.style.display = 'flex';
                submenuAccount.style.flexDirection = 'column';
                accountToggle.textContent = 'Mon compte ▲';
            }
        });
    </script>
    <script>
        document.querySelectorAll('.faq-item').forEach(item => {
            item.addEventListener('click', () => {
                item.classList.toggle('open');
            });
        });
    </script>
    <script>
 
            function logout() {
                localStorage.clear();
            window.location.href = "login.html";
        }
  
    </script>
</body>
</html>



















