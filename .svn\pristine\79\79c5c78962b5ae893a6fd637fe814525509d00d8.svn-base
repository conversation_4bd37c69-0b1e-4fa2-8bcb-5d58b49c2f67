<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Gestion de stock</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS + Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f8f9fa;
            color: #343a40;
            margin: 0;
        }

        #sidebar {
            width: 260px;
            background-color: #0d6efd;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 2rem;
            box-shadow: 3px 0 10px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

            #sidebar .nav-link {
                color: #cbd5f0;
                font-weight: 600;
                padding: 15px 25px;
                border-left: 4px solid transparent;
                transition: background-color 0.3s, border-color 0.3s;
            }

                #sidebar .nav-link:hover {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

                #sidebar .nav-link.active {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

            #sidebar .submenu {
                padding-left: 1.5rem;
                display: none;
                flex-direction: column;
            }

                #sidebar .submenu .nav-link {
                    padding: 10px 25px;
                    font-size: 0.9rem;
                    color: #a8bbea;
                }

        #main-content {
            margin-left: 260px;
            padding: 2rem;
        }

        .table th, .table td {
            vertical-align: middle;
        }

        .table img {
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .badge {
            font-size: 0.85rem;
            padding: 0.5em 0.75em;
        }

        .btn {
            border-radius: 30px;
            padding: 0.4rem 1rem;
            font-size: 0.875rem;
        }

        .btn-outline-primary:hover,
        .btn-outline-danger:hover {
            transform: scale(1.05);
        }

        canvas {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem;
        }

        .table-light th {
            background-color: #e9ecef;
        }

        .chart-small {
            width: 300px;
            height: 300px;
            margin: 0 auto;
        }
    </style>
</head>
<body>

    <!-- SIDEBAR -->
    <nav id="sidebar">
        <div class="text-center mb-4">
            <img src="images/Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white;">
            <h4 class="mt-2 fw-bold">TechInterv</h4>
            <small>Logiciel d'interventions</small>
        </div>

        <a href="Dashboard.html" class="nav-link active">Accueil</a>
        <a href="Clients.html" class="nav-link">Clients</a>
        <a href="Gestion de stock.html" class="nav-link">Gestion de stock</a>
        <a href="Mission.html" class="nav-link">Mission</a>
        <a href="MonCompte.html" class="nav-link">Mon compte</a>

        <!-- Sous-menu réduit uniquement à "Déconnexion" -->
        <div class="submenu">
            <a href="login.html" class="nav-link text-danger">
                <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
            </a>
        </div>
        <a href="Sites.html" class="nav-link">Sites</a>
        <a href="Rapport.html" class="nav-link">Rapport</a>
    </nav>

    <!-- CONTENU PRINCIPAL -->
    <div id="main-content">
        <div class="container mt-4">
            <h2 class="fw-bold text-primary">Gestion de stock</h2>
        
            <p class="text-muted">Suivez en temps réel vos équipements, machines et produits.</p>

            <!-- 📦 Tableau des stocks -->
            <div class="table-responsive mt-4">
                <table class="table table-hover shadow-sm">
                    <thead class="table-light">
                        <tr>
                            <th>Image</th>
                            <th>Nom</th>
                            <th>Catégorie</th>
                            <th>Quantité</th>
                            <th>État</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><img src="https://via.placeholder.com/50" alt="Machine"></td>
                            <td>Machine A</td>
                            <td>Production</td>
                            <td>12</td>
                            <td><span class="badge bg-success">Bon</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/50" alt="Pièce"></td>
                            <td>Filtre à air</td>
                            <td>Pièce</td>
                            <td>4</td>
                            <td><span class="badge bg-warning text-dark">À remplacer</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/50" alt="Tournevis électrique" style="width: 50px;"></td>
                            <td>Tournevis électrique</td>
                            <td>Outil</td>
                            <td>15</td>
                            <td><span class="badge bg-success">Fonctionnel</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/50" alt="Compresseur" style="width: 50px;"></td>
                            <td>Compresseur</td>
                            <td>Machine</td>
                            <td>2</td>
                            <td><span class="badge bg-danger">En panne</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/50" alt="Casque de protection" style="width: 50px;"></td>
                            <td>Casque de protection</td>
                            <td>Équipement</td>
                            <td>22</td>
                            <td><span class="badge bg-success">Disponible</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                            </td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/50" alt="Câble haute tension" style="width: 50px;"></td>
                            <td>Câble haute tension</td>
                            <td>Pièce</td>
                            <td>8</td>
                            <td><span class="badge bg-warning text-dark">Vérification</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Modifier</button>
                                <button class="btn btn-sm btn-outline-danger">Supprimer</button>
                            </td>
                        </tr>

                    </tbody>
                </table>
            </div>

            <!-- ➕ Ajouter un produit -->
            <div class="text-end mt-3">
                <button class="btn btn-primary px-4 py-2">+ Ajouter un asset</button>
            </div>

            <!-- 📊 Graphique des stocks -->
            <div class="text-center mt-5 mb-5">
                <h5 class="fw-semibold mb-3">Répartition des stocks par catégorie</h5>
                <canvas id="stockChart" class="chart-small"></canvas>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS + Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>

    <script>
        const ctx = document.getElementById('stockChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Machines', 'Pièces', 'Outils', 'Autres'],
                datasets: [{
                    label: 'Stock',
                    data: [12, 8, 6, 3],
                    backgroundColor: ['#0d6efd', '#198754', '#ffc107', '#6c757d'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 14
                            }
                        }
                    }
                }
            }
        });

        // Toggle sous-menu
        document.getElementById('accountToggle').addEventListener('click', function () {
            const submenu = document.getElementById('submenuAccount');
            submenu.style.display = submenu.style.display === 'flex' ? 'none' : 'flex';
        });
    </script>

</body>
</html>
