<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Créer un compte - Logiciel d'Interventions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        * {
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            margin: 0;
            font-family: 'Segoe UI', sans-serif;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .register-container {
            width: 100%;
            max-width: 700px;
            background-color: #fff;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

            .register-container h2 {
                text-align: center;
                color: #0d6efd; /* même couleur que btn-primary */
                font-weight: bold;
                margin-bottom: 30px;
            }

        .form-label {
            font-weight: 600;
            color: #333;
        }

        .form-control {
            border-radius: 8px;
            padding: 14px;
            font-size: 1rem;
            border: 1.5px solid #ccc;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

            .form-control:focus {
                border-color: #0b3d91;
                box-shadow: 0 0 8px rgba(11, 61, 145, 0.3);
                outline: none;
            }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            padding: 14px;
            font-size: 1.1rem;
            border-radius: 10px;
            width: 100%;
            margin-top: 15px;
            transition: background-color 0.3s ease;
        }

            .btn-primary:hover {
                background-color: #0b5ed7;
                border-color: #0b5ed7;
            }

        .form-check-label {
            font-size: 0.9rem;
            color: #555;
        }

        .bottom-links {
            text-align: center;
            margin-top: 25px;
            font-size: 0.9rem;
        }

            .bottom-links a {
                color: #0b3d91;
                text-decoration: none;
            }

                .bottom-links a:hover {
                    text-decoration: underline;
                    color: #084080;
                }

        .error-message {
            background-color: #f8d7da;
            color: #842029;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
        }
    </style>
</head>
<body>

    <div class="register-container">
        <h2>Créer un compte</h2>

        <div id="error-msg" class="error-message">
            Veuillez vérifier les informations saisies.
        </div>

        <form id="register-form" novalidate>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="firstName" class="form-label">Prénom</label>
                    <input type="text" class="form-control" id="firstName" required>
                </div>
                <div class="col-md-6">
                    <label for="lastName" class="form-label">Nom</label>
                    <input type="text" class="form-control" id="lastName" required>
                </div>
            </div>

            <div class="mb-3">
                <label for="email" class="form-label">Adresse e-mail</label>
                <input type="email" class="form-control" id="email" required>
            </div>

            <div class="mb-3">
                <label for="phone" class="form-label">Numéro de téléphone</label>
                <input type="tel" class="form-control" id="phone" required>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="password" class="form-label">Mot de passe</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                <div class="col-md-6">
                    <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                    <input type="password" class="form-control" id="confirmPassword" required>
                </div>
            </div>

            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="terms" required>
                <label class="form-check-label" for="terms">
                    J'accepte les <a href="#">conditions d'utilisation</a>.
                </label>
            </div>

            <button type="submit" class="btn btn-primary">Créer mon compte</button>

            <!-- Ajout de la section Google -->
            <div class="text-center my-3">
                <span class="text-muted">ou</span>
            </div>

            <div class="d-grid">
                <a href="#" class="btn btn-outline-dark" style="border-radius: 8px;">
                    <img src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg" width="20" class="me-2" alt="Google logo">
                    Continuer avec Google
                </a>
            </div>

            <div class="bottom-links">
                Déjà inscrit ? <a href="login.html">Se connecter</a>
            </div>
        </form>
    </div>

    <script>
        const form = document.getElementById('register-form');
        const errorMsg = document.getElementById('error-msg');

        form.addEventListener('submit', e => {
            e.preventDefault();
            const pwd = document.getElementById('password').value.trim();
            const confirm = document.getElementById('confirmPassword').value.trim();
            const email = document.getElementById('email').value.trim();
            const terms = document.getElementById('terms').checked;

            if (!email || !pwd || !confirm || !terms) {
                errorMsg.textContent = "Tous les champs sont obligatoires.";
                errorMsg.style.display = "block";
                return;
            }

            if (pwd !== confirm) {
                errorMsg.textContent = "Les mots de passe ne correspondent pas.";
                errorMsg.style.display = "block";
                return;
            }

            errorMsg.style.display = "none";
            alert("Inscription réussie !");
            window.location.href = "Dashboard.html";
        });
    </script>

</body>
</html>

