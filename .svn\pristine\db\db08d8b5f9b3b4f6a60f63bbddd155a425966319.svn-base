<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Connexion</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #fff;
            min-height: 100vh;
            margin: 0;
            padding: 60px 15px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            width: 100%;
            max-width: 600px;
            background-color: #fff;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

            .login-container h2 {
                text-align: center;
                color: #0d6efd;
                font-weight: bold;
                margin-bottom: 30px;
            }

        .form-label {
            font-weight: 600;
            color: #333;
        }

        .form-control {
            border-radius: 8px;
            padding: 14px;
            font-size: 1rem;
            border: 1.5px solid #ccc;
        }

            .form-control:focus {
                border-color: #0d6efd;
                box-shadow: 0 0 8px rgba(13, 110, 253, 0.3);
                outline: none;
            }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            padding: 14px;
            font-size: 1.1rem;
            border-radius: 10px;
            width: 100%;
            margin-top: 10px;
        }

        .form-check-label {
            font-size: 0.9rem;
            color: #555;
        }

        .bottom-links {
            text-align: center;
            margin-top: 25px;
            font-size: 0.9rem;
        }

            .bottom-links a {
                color: #0d6efd;
                text-decoration: none;
            }

                .bottom-links a:hover {
                    text-decoration: underline;
                }

        .error-message {
            background-color: #f8d7da;
            color: #842029;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
        }
    </style>
</head>
<body>

    <div class="login-container">
        <h2>Connexion</h2>

        <div id="error-msg" class="error-message">
            Adresse e-mail ou mot de passe incorrect.
        </div>

        <form id="login-form" novalidate>
            <div class="mb-3">
                <label for="email" class="form-label">Adresse e-mail</label>
                <input type="email" class="form-control" id="email" required>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">Mot de passe</label>
                <input type="password" class="form-control" id="password" required>
            </div>

            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="rememberMe">
                <label class="form-check-label" for="rememberMe">
                    Se souvenir de moi
                </label>
            </div>

            <button type="submit" class="btn btn-primary">Se connecter</button>

            <!-- Partie Google -->
            <div class="text-center my-3">
                <span class="text-muted">ou</span>
            </div>

            <div class="d-grid">
                <a href="#" class="btn btn-outline-dark" style="border-radius: 8px;">
                    <img src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg" width="20" class="me-2">
                    Continuer avec Google
                </a>
            </div>

            <div class="bottom-links mt-4">
                <a href="#">Mot de passe oublié ?</a><br />
                <a href="register.html">Créer un compte</a>
            </div>
        </form>
    </div>

    <script>
        const form = document.getElementById('login-form');
        const errorMsg = document.getElementById('error-msg');

        form.addEventListener('submit', e => {
            e.preventDefault();
            const email = form.email.value.trim();
            const password = form.password.value.trim();

            if (email === "" || password === "") {
                errorMsg.textContent = "Veuillez remplir tous les champs.";
                errorMsg.style.display = "block";
                return;
            }

            if (email !== "<EMAIL>" || password !== "123456") {
                errorMsg.textContent = "Adresse e-mail ou mot de passe incorrect.";
                errorMsg.style.display = "block";
                return;
            }

            errorMsg.style.display = "none";
            alert("Connexion réussie !");
        });
    </script>

</body>
</html>
