<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Mon <PERSON></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">

    <!-- Sidebar -->
    <nav id="sidebar" class="d-flex flex-column">
        <!-- En-tête de la sidebar -->
        <div class="text-center mb-4">
            <img src="images/Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white; margin-bottom: 10px;">
            <h4 style="margin: 0; font-weight: 700;">TechInterv</h4>
            <small>Logiciel d'interventions</small>
        </div>

        <a href="Dashboard.html" class="nav-link active">Accueil</a>
        <a href="Clients.html" class="nav-link">Clients</a>
        <a href="Gestion de stock.html" class="nav-link">Gestion de stock</a>
        <a href="Mission.html" class="nav-link">Mission</a>
        <a href="MonCompte.html" class="nav-link">Mon compte</a>

        <!-- Sous-menu réduit uniquement à "Déconnexion" -->
        <div class="submenu">
            <a href="login.html" class="nav-link text-danger">
                <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
            </a>
        </div>
        <a href="Sites.html" class="nav-link">Sites</a>
        <a href="Rapport.html" class="nav-link">Rapport</a>

        <a href="Login.html" onclick="logout()" class="nav-link logout-link mt-auto d-flex align-items-center gap-2">
            <i class="bi bi-box-arrow-right"></i>
            <span>Se d&#233;connecter</span>
        </a>

    </nav>
    <div class="container py-5">
        <h2 class="mb-4 text-primary">👤 Mon Compte</h2>

        <!-- Informations personnelles -->
        <div class="card mb-4 p-3">
            <h5>Informations personnelles</h5>
            <div class="row">
                <div class="col-md-6">
                    <label>Nom complet</label>
                    <input type="text" class="form-control" value="Youssef El Amrani">
                </div>
                <div class="col-md-6">
                    <label>Email</label>
                    <input type="email" class="form-control" value="<EMAIL>">
                </div>
                <div class="col-md-6 mt-2">
                    <label>Téléphone</label>
                    <input type="text" class="form-control" value="+212 6 12 34 56 78">
                </div>
                <div class="col-md-6 mt-2">
                    <label>Rôle</label>
                    <input type="text" class="form-control" value="Technicien" disabled>
                </div>
            </div>
        </div>

        <!-- Sécurité -->
        <div class="card mb-4 p-3">
            <h5>Sécurité</h5>
            <div class="row">
                <div class="col-md-6">
                    <label>Mot de passe actuel</label>
                    <input type="password" class="form-control">
                </div>
                <div class="col-md-6">
                    <label>Nouveau mot de passe</label>
                    <input type="password" class="form-control">
                </div>
            </div>
            <button class="btn btn-warning mt-3">Changer le mot de passe</button>
        </div>

        <!-- Préférences -->
        <div class="card mb-4 p-3">
            <h5>Préférences</h5>
            <div class="row">
                <div class="col-md-4">
                    <label>Langue</label>
                    <select class="form-select">
                        <option selected>Français</option>
                        <option>Anglais</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label>Thème</label>
                    <select class="form-select">
                        <option>Clair</option>
                        <option>Sombre</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label>Notifications</label>
                    <select class="form-select">
                        <option>Email uniquement</option>
                        <option>Email + SMS</option>
                        <option>Pas de notifications</option>
                    </select>
                </div>
            </div>
        </div>
        <!-- Changer de rôle -->
        <div class="card mb-4 p-3">
            <h5>👥 Changer de rôle</h5>
            <p>Actuellement en tant que : <strong>Technicien</strong></p>
            <select class="form-select mb-2" aria-label="Changer de rôle">
                <option selected>Technicien</option>
                <option>Admin</option>
                <option>Chef d'équipe</option>
            </select>
            <button class="btn btn-secondary">Changer de rôle</button>
        </div>

        <!-- Historique des interventions personnelles -->
        <div class="card mb-4 p-3">
            <h5>📅 Historique des interventions</h5>
            <ul class="list-group list-group-flush">
                <li class="list-group-item">🔧 15/07/2025 - Réparation fuite - Site OCP Safi</li>
                <li class="list-group-item">🔥 13/07/2025 - Installation chaudière - Résidence Al Qods</li>
                <li class="list-group-item">🧊 10/07/2025 - Entretien clim - Hôtel Atlas</li>
            </ul>
            <a href="#" class="btn btn-outline-primary btn-sm mt-3">Voir tout l'historique</a>
        </div>

        <!-- Paramètres de rappels -->
        <div class="card mb-4 p-3">
            <h5>🔔 Paramètres de rappels</h5>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="emailReminder" checked>
                <label class="form-check-label" for="emailReminder">
                    Recevoir un rappel par email avant chaque intervention
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="smsReminder">
                <label class="form-check-label" for="smsReminder">
                    Recevoir un rappel SMS (optionnel)
                </label>
            </div>
            <button class="btn btn-primary mt-3">Enregistrer les préférences</button>
        </div>

        <!-- Supprimer compte -->
        <div class="card border-danger p-3">
            <h5 class="text-danger">Supprimer le compte</h5>
            <p class="text-muted">Attention : cette action est irréversible.</p>
            <button class="btn btn-outline-danger">Supprimer mon compte</button>
        </div>
    </div>
    <style>
        #sidebar {
            width: 260px;
            background-color: #0d6efd; /* bleu bootstrap standard, plus clair */
            color: white;
            height: 100vh; /* pleine hauteur */
            position: fixed;
            padding-top: 2rem;
            box-shadow: 3px 0 10px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            user-select: none;
            overflow-y: auto; /* scroll si sidebar trop longue */
        }



            #sidebar .nav-link {
                color: #cbd5f0; /* bleu clair */
                font-weight: 600;
                padding: 15px 25px;
                border-left: 4px solid transparent;
                transition: background-color 0.3s, border-color 0.3s;
                cursor: pointer;
            }

                #sidebar .nav-link:hover {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

                #sidebar .nav-link.active {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

            #sidebar .submenu {
                padding-left: 1.5rem;
                font-weight: 500;
                display: none;
                flex-direction: column;
            }

                #sidebar .submenu .nav-link {
                    padding: 10px 25px;
                    font-size: 0.9rem;
                    color: #a8bbea;
                    border-left: none;
                }

                    #sidebar .submenu .nav-link:hover {
                        color: #d0e1ff;
                        background-color: transparent;
                    }
    </style>
</body>
</html>
