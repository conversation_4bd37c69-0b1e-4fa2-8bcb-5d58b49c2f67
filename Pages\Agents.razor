@page "/agents"

<PageTitle>Agents IA - Gestion des interventions</PageTitle>

<style>
    /* Professional Orange Theme Data Grid */
    .carsystems-container {
        background: #f5f5f5;
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .carsystems-header {
        background: linear-gradient(135deg, #ff8c00, #ff7700);
        color: white;
        padding: 12px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .carsystems-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
    }

    .page-selector {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 20px;
        padding: 6px 16px;
        color: white;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
    }

    .filters-section {
        background: white;
        padding: 16px 20px;
        border-bottom: 1px solid #e0e0e0;
    }

    .filters-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: #666;
        margin-bottom: 12px;
    }

    .filters-row {
        display: flex;
        gap: 12px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-input {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 0.85rem;
        width: 160px;
        background: white;
    }

    .filter-input:focus {
        outline: none;
        border-color: #ff8c00;
        box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.1);
    }

    .btn-apply {
        background: #ff8c00;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 0.85rem;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.2s;
    }

    .btn-apply:hover {
        background: #e67c00;
    }

    .btn-export {
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 0.85rem;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.2s;
    }

    .btn-export:hover {
        background: #5a6268;
    }

    .btn-clear {
        background: transparent;
        color: #ff8c00;
        border: 1px solid #ff8c00;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 0.8rem;
        cursor: pointer;
        margin-left: auto;
    }

    .btn-clear:hover {
        background: #ff8c00;
        color: white;
    }

    /* Sidebar Styling */
    #sidebar {
        width: 260px;
        background-color: #1976d2;
        color: white;
        height: 100vh;
        position: fixed;
        padding-top: 2rem;
        box-shadow: 2px 0 8px rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
        user-select: none;
        overflow-y: auto;
        z-index: 1000;
    }

    #sidebar .nav-link {
        color: #e3f2fd;
        font-weight: 500;
        padding: 16px 24px;
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        font-size: 0.95rem;
    }

    #sidebar .nav-link:hover {
        background-color: #1565c0;
        color: white;
        border-left-color: #42a5f5;
    }

    #sidebar .nav-link.active {
        background-color: #1565c0;
        color: white;
        border-left-color: white;
        font-weight: 600;
    }

    /* Main Content */
    .main-content {
        margin-left: 260px;
        min-height: 100vh;
    }

    .content-wrapper {
        padding: 32px 40px;
    }

    /* Professional Orange Theme Data Grid */
    .grid-wrapper {
        background: white;
        border: 1px solid #ddd;
        border-radius: 0;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Filters Bar Above Grid */
    .filters-bar {
        background: #f8f9fa;
        padding: 12px 16px;
        border-bottom: 1px solid #ddd;
        display: flex;
        gap: 12px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filters-bar label {
        font-size: 0.85rem;
        font-weight: 600;
        color: #666;
        margin-right: 4px;
    }

    .filter-input {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px 10px;
        font-size: 0.8rem;
        width: 140px;
        background: white;
    }

    .filter-input:focus {
        outline: none;
        border-color: #ff8c00;
        box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.1);
    }

    .btn-filter {
        background: #ff8c00;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.2s;
    }

    .btn-filter:hover {
        background: #e67c00;
    }

    /* Professional Grid Styling */
    .e-grid {
        border: none !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        font-size: 0.75rem !important;
    }

    .e-grid .e-gridheader {
        background: linear-gradient(135deg, #ff8c00, #ff7700) !important;
        border-bottom: 1px solid #e67c00 !important;
    }

    .e-grid .e-headercell {
        background: transparent !important;
        border-right: 1px solid rgba(255,255,255,0.2) !important;
        padding: 8px 10px !important;
        font-size: 0.7rem !important;
        font-weight: 600 !important;
        color: white !important;
        text-transform: uppercase !important;
        letter-spacing: 0.3px !important;
        height: 32px !important;
    }

    .e-grid .e-headercell:last-child {
        border-right: none !important;
    }

    .e-grid .e-row {
        border-bottom: 1px solid #f0f0f0 !important;
        height: 26px !important;
    }

    .e-grid .e-row:hover {
        background: #fff8f0 !important;
    }

    .e-grid .e-altrow {
        background: #fafafa !important;
    }

    .e-grid .e-altrow:hover {
        background: #fff8f0 !important;
    }

    .e-grid .e-rowcell {
        padding: 4px 10px !important;
        border-right: 1px solid #f5f5f5 !important;
        font-size: 0.7rem !important;
        color: #333 !important;
        vertical-align: middle !important;
        line-height: 1.2 !important;
    }

    .e-grid .e-rowcell:last-child {
        border-right: none !important;
    }

    /* Hide Syncfusion Accessibility Elements */
    .e-grid .e-headercell .e-sortfilterdiv .e-ascending,
    .e-grid .e-headercell .e-sortfilterdiv .e-descending,
    .e-grid .e-headercell .e-sortfilterdiv .e-sortnone,
    .e-grid .e-headercell .e-headertext::after,
    .e-grid .e-headercell::after,
    .e-grid .e-headercell .e-hide,
    .e-grid .e-gridheader table caption {
        display: none !important;
    }

    /* Professional Pagination with Orange Theme */
    .e-grid .e-pager {
        background: white !important;
        border-top: 1px solid #ddd !important;
        padding: 8px 16px !important;
        font-size: 0.7rem !important;
    }

    .e-grid .e-pagercontainer {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .e-grid .e-link {
        background: white !important;
        border: 1px solid #ddd !important;
        color: #666 !important;
        padding: 2px 6px !important;
        margin: 0 1px !important;
        border-radius: 2px !important;
        font-size: 0.7rem !important;
        min-width: 20px !important;
        text-align: center !important;
        height: 22px !important;
        line-height: 18px !important;
    }

    .e-grid .e-link:hover {
        background: #f8f9fa !important;
        border-color: #ccc !important;
    }

    .e-grid .e-currentitem {
        background: #ff8c00 !important;
        color: white !important;
        border-color: #ff8c00 !important;
        font-weight: 600 !important;
    }

    .e-grid .e-pagerinfo {
        color: #666 !important;
        font-size: 0.7rem !important;
    }

    /* Simple Action Buttons */
    .action-buttons {
        display: flex;
        gap: 4px;
        justify-content: center;
    }

    .btn-action {
        padding: 4px 8px;
        border: 1px solid;
        border-radius: 4px;
        font-size: 0.75rem;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 4px;
    }

    .btn-view {
        background: #007bff;
        border-color: #007bff;
        color: white;
    }

    .btn-edit {
        background: #ffc107;
        border-color: #ffc107;
        color: #212529;
    }

    .btn-delete {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
    }



    /* Enhanced Responsive Design */
    @@media (max-width: 1200px) {
        .content-wrapper {
            padding: 24px 20px;
        }

        .e-grid .e-rowcell,
        .e-grid .e-headercell {
            padding: 8px 6px !important;
            font-size: 0.8rem !important;
        }

        .btn-action {
            padding: 6px 10px !important;
            font-size: 0.75rem !important;
            min-width: 60px !important;
        }
    }

    @@media (max-width: 768px) {
        #sidebar {
            position: static;
            width: 100%;
            height: auto;
        }

        .main-content {
            margin-left: 0;
        }

        .content-wrapper {
            padding: 16px;
        }

        .page-header {
            padding: 20px;
        }

        .page-header h1 {
            font-size: 1.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }

        .grid-container {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .e-grid {
            min-width: 800px !important;
        }

        .action-buttons {
            flex-direction: column;
            gap: 2px;
        }

        .btn-action {
            min-width: 50px !important;
            padding: 4px 8px !important;
            font-size: 0.7rem !important;
        }

        .status-badge,
        .model-badge {
            font-size: 0.65rem !important;
            padding: 2px 6px !important;
        }
    }
</style>

<!-- 🔹 Sidebar -->
<nav id="sidebar">
    <div class="text-center mb-4">
        <img src="images/Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white; margin-bottom: 10px;">
        <h4 style="margin: 0; font-weight: 700;">TechInterv</h4>
        <small>Logiciel d'interventions</small>
    </div>

    <a href="/dashboard" class="nav-link">Accueil</a>
    <a href="/clients" class="nav-link">Clients</a>
    <a href="/agents" class="nav-link active">Agents IA</a>
    <a href="#" class="nav-link" onclick="return false;">Gestion de stock</a>
    <a href="#" class="nav-link" onclick="return false;">Mission</a>
    <a href="#" class="nav-link" onclick="return false;">Mon compte</a>

    <!-- Sous-menu réduit uniquement à "Déconnexion" -->
    <div class="submenu">
        <a href="/login" class="nav-link text-danger">
            <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
        </a>
    </div>

    <a href="/sites" class="nav-link">Sites</a>
    <a href="/rapport" class="nav-link">Rapport</a>
</nav>

<div class="main-content">
    <div class="content-wrapper">
        <div class="agents-container">
            <h1 class="page-title">
                <i class="bi bi-cpu"></i>
                Gestion des Agents IA
            </h1>

            <div class="grid-wrapper">
                <!-- Professional Filters Bar -->
                <div class="filters-bar">
                    <label>Filtres</label>
                    <input type="text" class="filter-input" placeholder="Rechercher un client..." @bind="searchFilter" @bind:event="oninput" />
                    <input type="text" class="filter-input" placeholder="Filtrer par statut..." @bind="statusFilter" @bind:event="oninput" />
                    <select class="filter-input" @bind="typeFilter">
                        <option value="">Sélectionner le type...</option>
                        <option value="GPT-4">GPT-4</option>
                        <option value="Claude">Claude</option>
                        <option value="Gemini">Gemini</option>
                    </select>
                    <input type="date" class="filter-input" @bind="dateFromFilter" />
                    <input type="date" class="filter-input" @bind="dateToFilter" />
                    <button class="btn-filter" @onclick="ApplyFilters">Appliquer</button>
                    <button class="btn-filter" style="background: #6c757d;" @onclick="ClearFilters">Effacer tout</button>
                </div>

                <!-- Professional Data Grid with Orange Theme -->
                <SfGrid DataSource="@filteredAgents"
                        AllowPaging="true"
                        AllowSorting="true"
                        Height="500px"
                        EnableAltRow="true"
                        GridLines="GridLine.Both"
                        PageSettings="@(new PageSettingsModel { PageSize = 15, PageSizes = new string[] { "15", "25", "50", "100" } })">

                    <GridColumns>
                        <GridColumn Field="AIAgentID" HeaderText="N°FACTURE" Width="90" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="Model" HeaderText="CODE DEVIS" Width="100" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="Model" HeaderText="TYPE" Width="80" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="Name" HeaderText="CLIENT" Width="180"></GridColumn>
                        <GridColumn Field="Description" HeaderText="LIBELLE" Width="160"></GridColumn>
                        <GridColumn Field="ClientID" HeaderText="MONTANT" Width="90" TextAlign="TextAlign.Right"></GridColumn>
                        <GridColumn Field="IsDeleted" HeaderText="TVA" Width="60" TextAlign="TextAlign.Center">
                            <Template>
                                @{
                                    var agent = (context as AIAgent);
                                }
                                <span>N</span>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="CreatedDate" HeaderText="DATE FACT" Width="90" Format="dd/MM/yyyy" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="CreatedDate" HeaderText="DATE ECH" Width="90" Format="dd/MM/yyyy" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="IsDeleted" HeaderText="FACTUEL" Width="70" TextAlign="TextAlign.Center">
                            <Template>
                                @{
                                    var agent = (context as AIAgent);
                                }
                                <span>AC</span>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="IsDeleted" HeaderText="RETARD" Width="70" TextAlign="TextAlign.Center">
                            <Template>
                                @{
                                    var agent = (context as AIAgent);
                                }
                                <span>N</span>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="IsDeleted" HeaderText="FACADRE" Width="70" TextAlign="TextAlign.Center">
                            <Template>
                                @{
                                    var agent = (context as AIAgent);
                                }
                                <span>F</span>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="ClientID" HeaderText="SOLDE" Width="90" TextAlign="TextAlign.Right"></GridColumn>
                        <GridColumn Field="IsDeleted" HeaderText="TYPE TVA" Width="80" TextAlign="TextAlign.Center">
                            <Template>
                                @{
                                    var agent = (context as AIAgent);
                                }
                                <span>N</span>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="Model" HeaderText="CODE TVA" Width="80" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="IsDeleted" HeaderText="CODE PRECISION" Width="110" TextAlign="TextAlign.Center">
                            <Template>
                                @{
                                    var agent = (context as AIAgent);
                                }
                                <span>ACCEPT</span>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="IsDeleted" HeaderText="CREDIT" Width="70" TextAlign="TextAlign.Center">
                            <Template>
                                @{
                                    var agent = (context as AIAgent);
                                }
                                <span>0,00</span>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="IsDeleted" HeaderText="DEBIT" Width="70" TextAlign="TextAlign.Center">
                            <Template>
                                @{
                                    var agent = (context as AIAgent);
                                }
                                <span>0,00</span>
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        </div>
    </div>
</div>

@code {
    private List<AIAgent> AgentsList = new();
    private List<AIAgent> filteredAgents = new();

    // Filter properties with automatic filtering
    private string _searchFilter = "";
    private string searchFilter
    {
        get => _searchFilter;
        set { _searchFilter = value; ApplyFilters(); }
    }

    private string _statusFilter = "";
    private string statusFilter
    {
        get => _statusFilter;
        set { _statusFilter = value; ApplyFilters(); }
    }

    private string _typeFilter = "";
    private string typeFilter
    {
        get => _typeFilter;
        set { _typeFilter = value; ApplyFilters(); }
    }

    private DateTime? _dateFromFilter;
    private DateTime? dateFromFilter
    {
        get => _dateFromFilter;
        set { _dateFromFilter = value; ApplyFilters(); }
    }

    private DateTime? _dateToFilter;
    private DateTime? dateToFilter
    {
        get => _dateToFilter;
        set { _dateToFilter = value; ApplyFilters(); }
    }

    public class AIAgent
    {
        public int AIAgentID { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Instructions { get; set; } = "";
        public string Prompt { get; set; } = "";
        public string Model { get; set; } = "";
        public int? ClientID { get; set; }
        public bool? IsDeleted { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    protected override void OnInitialized()
    {
        // Generate comprehensive sample data
        AgentsList = new List<AIAgent>
        {
            new AIAgent { AIAgentID = 1, Name = "Agent Support Client", Description = "Agent de support client automatisé pour répondre aux questions fréquentes", Instructions = "Répondre aux questions des clients avec empathie", Prompt = "Tu es un assistant de support professionnel", Model = "GPT-4", ClientID = 101, IsDeleted = false },
            new AIAgent { AIAgentID = 2, Name = "Agent Diagnostic Technique", Description = "Agent spécialisé dans le diagnostic des problèmes techniques complexes", Instructions = "Analyser les problèmes techniques et proposer des solutions", Prompt = "Tu es un expert technique avec 10 ans d'expérience", Model = "Claude-3", ClientID = 102, IsDeleted = false },
            new AIAgent { AIAgentID = 3, Name = "Agent Planification", Description = "Agent d'optimisation des plannings et ressources d'intervention", Instructions = "Optimiser les plannings en fonction des priorités", Prompt = "Tu es un planificateur expert en logistique", Model = "GPT-4", ClientID = 103, IsDeleted = false },
            new AIAgent { AIAgentID = 4, Name = "Agent Rapport", Description = "Agent de génération automatique de rapports détaillés", Instructions = "Créer des rapports complets et structurés", Prompt = "Tu es un rédacteur technique professionnel", Model = "Claude-3", ClientID = 104, IsDeleted = true },
            new AIAgent { AIAgentID = 5, Name = "Agent Maintenance Prédictive", Description = "Agent de prédiction et planification de la maintenance préventive", Instructions = "Prédire les besoins de maintenance basés sur les données", Prompt = "Tu es un expert en maintenance prédictive", Model = "GPT-4", ClientID = 105, IsDeleted = false },
            new AIAgent { AIAgentID = 6, Name = "Agent Formation", Description = "Agent de formation et accompagnement des nouveaux techniciens", Instructions = "Former et guider les techniciens débutants", Prompt = "Tu es un formateur pédagogue expérimenté", Model = "Claude-3", ClientID = 106, IsDeleted = false },
            new AIAgent { AIAgentID = 7, Name = "Agent Contrôle Qualité", Description = "Agent de vérification et validation de la qualité des interventions", Instructions = "Vérifier la conformité et qualité des interventions", Prompt = "Tu es un contrôleur qualité rigoureux", Model = "GPT-4", ClientID = 107, IsDeleted = false },
            new AIAgent { AIAgentID = 8, Name = "Agent Facturation", Description = "Agent de gestion automatisée de la facturation et comptabilité", Instructions = "Gérer les factures et suivre les paiements", Prompt = "Tu es un comptable méticuleux et organisé", Model = "Claude-3", ClientID = 108, IsDeleted = false },
            new AIAgent { AIAgentID = 9, Name = "Agent Sécurité", Description = "Agent de surveillance et gestion des protocoles de sécurité", Instructions = "Surveiller et faire respecter les protocoles de sécurité", Prompt = "Tu es un expert en sécurité industrielle", Model = "GPT-4", ClientID = 109, IsDeleted = false },
            new AIAgent { AIAgentID = 10, Name = "Agent Analytics", Description = "Agent d'analyse de données et génération d'insights métier", Instructions = "Analyser les données et fournir des insights", Prompt = "Tu es un data analyst expert", Model = "Claude-3", ClientID = 110, IsDeleted = false },
            new AIAgent { AIAgentID = 11, Name = "Agent Communication", Description = "Agent de gestion des communications internes et externes", Instructions = "Gérer les communications et notifications", Prompt = "Tu es un expert en communication", Model = "GPT-4", ClientID = 111, IsDeleted = false },
            new AIAgent { AIAgentID = 12, Name = "Agent Archivé", Description = "Ancien agent de test qui n'est plus utilisé", Instructions = "Agent de test obsolète", Prompt = "Agent de test", Model = "GPT-3.5", ClientID = 112, IsDeleted = true }
        };

        // Initialize filtered list
        filteredAgents = AgentsList.ToList();
    }

    private void OnFilterChanged()
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        filteredAgents = AgentsList.Where(agent =>
            (string.IsNullOrEmpty(searchFilter) ||
             agent.Name.Contains(searchFilter, StringComparison.OrdinalIgnoreCase) ||
             agent.Description.Contains(searchFilter, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(statusFilter) ||
             (agent.IsDeleted == true ? "Supprimé" : "Actif").Contains(statusFilter, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(typeFilter) ||
             agent.Model.Contains(typeFilter, StringComparison.OrdinalIgnoreCase))
        ).ToList();

        StateHasChanged();
    }

    private void ClearFilters()
    {
        _searchFilter = "";
        _statusFilter = "";
        _typeFilter = "";
        _dateFromFilter = null;
        _dateToFilter = null;
        filteredAgents = AgentsList.ToList();
        StateHasChanged();
    }

    private void ViewAgent(int agentId)
    {
        Console.WriteLine($"View Agent {agentId} clicked");
    }

    private void EditAgent(int agentId)
    {
        Console.WriteLine($"Edit Agent {agentId} clicked");
    }

    private void DeleteAgent(int agentId)
    {
        Console.WriteLine($"Delete Agent {agentId} clicked");
    }

    private void AddAgent()
    {
        Console.WriteLine("Add Agent clicked");
    }
}
