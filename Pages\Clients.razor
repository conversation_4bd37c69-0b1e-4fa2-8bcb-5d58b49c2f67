@page "/clients"

<PageTitle>Clients - Gestion des interventions</PageTitle>

<style>
    body {
        background-color: #f5f6fa;
        margin: 0;
        padding: 0;
    }

    #sidebar {
        width: 260px;
        background-color: #0d6efd;
        color: white;
        height: 100vh;
        position: fixed;
        padding-top: 2rem;
        box-shadow: 3px 0 10px rgba(0,0,0,0.15);
        display: flex;
        flex-direction: column;
        user-select: none;
        overflow-y: auto;
    }

        #sidebar .nav-link {
            color: #cbd5f0;
            font-weight: 600;
            padding: 15px 25px;
            border-left: 4px solid transparent;
            transition: background-color 0.3s, border-color 0.3s;
        }

            #sidebar .nav-link:hover {
                background-color: #0d4fbd;
                color: white;
                border-left-color: #0d6efd;
            }

            #sidebar .nav-link.active {
                background-color: #0d4fbd;
                color: white;
                border-left-color: #0d6efd;
            }

        #sidebar .submenu {
            padding-left: 1.5rem;
            font-weight: 500;
            display: none;
            flex-direction: column;
        }

            #sidebar .submenu .nav-link {
                padding: 10px 25px;
                font-size: 0.9rem;
                color: #a8bbea;
                border-left: none;
            }

                #sidebar .submenu .nav-link:hover {
                    color: #d0e1ff;
                    background-color: transparent;
                }

    .main-content {
        margin-left: 260px;
        padding: 40px 20px;
    }

    .client-photo {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 50%;
    }

    .client-table th, .client-table td {
        vertical-align: middle;
        text-align: center;
    }

    .btn-action {
        margin: 0 2px;
    }

    .add-client-btn {
        margin-top: 30px;
        padding: 10px 30px;
        font-size: 18px;
    }

    .card {
        border-radius: 16px;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    @@media (max-width: 768px) {
        #sidebar {
            position: static;
            width: 100%;
            height: auto;
        }

        .main-content {
            margin-left: 0;
        }
    }
</style>

<!-- 🔹 Sidebar -->
<nav id="sidebar">
    <div class="text-center mb-4">
        <img src="images/Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white; margin-bottom: 10px;">
        <h4 style="margin: 0; font-weight: 700;">TechInterv</h4>
        <small>Logiciel d'interventions</small>
    </div>

    <a href="/dashboard" class="nav-link">Accueil</a>
    <a href="/clients" class="nav-link active">Clients</a>
    <a href="/agents" class="nav-link">Agents IA</a>
    <a href="#" class="nav-link" onclick="return false;">Gestion de stock</a>
    <a href="#" class="nav-link" onclick="return false;">Mission</a>
    <a href="#" class="nav-link" onclick="return false;">Mon compte</a>

    <!-- Sous-menu réduit uniquement à "Déconnexion" -->
    <div class="submenu">
        <a href="/login" class="nav-link text-danger">
            <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
        </a>
    </div>

    <a href="/sites" class="nav-link">Sites</a>
    <a href="/rapport" class="nav-link">Rapport</a>
</nav>

<!-- 🔸 Main -->
<div class="main-content">
    <div class="text-center mb-5">
        <h2 class="fw-bold text-primary">Liste des clients</h2>
        <p class="text-muted">Gérez vos clients via ce tableau</p>
    </div>

    <div class="card p-4">
        <div class="table-responsive">
            <table class="table table-hover align-middle client-table">
                <thead class="table-light">
                    <tr>
                        <th>Photo</th>
                        <th>Nom</th>
                        <th>Prénom</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><img src="images/Profile.png" alt="Client" class="client-photo"></td>
                        <td>Lemoine</td>
                        <td>Sarah</td>
                        <td>
                            <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                            <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                            <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                        </td>
                    </tr>
                    <tr>
                        <td><img src="images/Profile.png" alt="Client" class="client-photo"></td>
                        <td>Dupont</td>
                        <td>Marc</td>
                        <td>
                            <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                            <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                            <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                        </td>
                    </tr>
                    <tr>
                        <td><img src="images/Profile.png" alt="Client" class="client-photo"></td>
                        <td>Benali</td>
                        <td>Youssef</td>
                        <td>
                            <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                            <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                            <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                        </td>
                    </tr>
                    <tr>
                        <td><img src="images/Profile.png" alt="Client" class="client-photo"></td>
                        <td>Mehdi</td>
                        <td>Karim</td>
                        <td>
                            <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                            <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                            <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                        </td>
                    </tr>
                    <tr>
                        <td><img src="images/Profile.png" alt="Client" class="client-photo"></td>
                        <td>Alami</td>
                        <td>Fatima</td>
                        <td>
                            <button class="btn btn-outline-primary btn-sm btn-action"><i class="bi bi-pencil-square"></i> Éditer</button>
                            <button class="btn btn-outline-warning btn-sm btn-action"><i class="bi bi-gear"></i> Modifier</button>
                            <button class="btn btn-outline-danger btn-sm btn-action"><i class="bi bi-trash"></i> Supprimer</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="text-center">
            <button class="btn btn-primary add-client-btn">
                <i class="bi bi-plus-circle me-2"></i>Ajouter un nouveau client
            </button>
        </div>
    </div>
</div>
