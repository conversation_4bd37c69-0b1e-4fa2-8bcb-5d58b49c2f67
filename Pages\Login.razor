@page "/login"
@inject NavigationManager Navigation

<PageTitle>Connexion</PageTitle>

<style>
    body {
        font-family: 'Segoe UI', sans-serif;
        background-color: #fff;
        min-height: 100vh;
        margin: 0;
        padding: 60px 15px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .login-container {
        width: 100%;
        max-width: 600px;
        background-color: #fff;
        border-radius: 12px;
        padding: 40px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

        .login-container h2 {
            text-align: center;
            color: #0d6efd;
            font-weight: bold;
            margin-bottom: 30px;
        }

    .form-label {
        font-weight: 600;
        color: #333;
    }

    .form-control {
        border-radius: 8px;
        padding: 14px;
        font-size: 1rem;
        border: 1.5px solid #ccc;
    }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 8px rgba(13, 110, 253, 0.3);
        }

    .btn-primary {
        background-color: #0d6efd;
        border: none;
        border-radius: 8px;
        padding: 14px;
        font-size: 1.1rem;
        font-weight: 600;
        width: 100%;
        transition: background-color 0.3s ease;
    }

        .btn-primary:hover {
            background-color: #0b5ed7;
        }

    .text-center a {
        color: #0d6efd;
        text-decoration: none;
        font-weight: 500;
    }

        .text-center a:hover {
            text-decoration: underline;
        }

    .alert {
        border-radius: 8px;
        font-size: 0.95rem;
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .form-check-label {
        font-size: 0.95rem;
        color: #555;
    }

    .mb-3 {
        margin-bottom: 1.5rem;
    }

    .text-muted {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .divider {
        text-align: center;
        margin: 25px 0;
        position: relative;
        color: #999;
        font-size: 0.9rem;
    }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #ddd;
            z-index: 1;
        }

        .divider span {
            background-color: #fff;
            padding: 0 15px;
            position: relative;
            z-index: 2;
        }

    .btn-outline-secondary {
        border: 1.5px solid #6c757d;
        color: #6c757d;
        border-radius: 8px;
        padding: 14px;
        font-size: 1rem;
        font-weight: 500;
        width: 100%;
        transition: all 0.3s ease;
    }

        .btn-outline-secondary:hover {
            background-color: #6c757d;
            color: #fff;
        }

    .social-login {
        margin-top: 20px;
    }

    .btn-google {
        background-color: #db4437;
        border: none;
        color: white;
        border-radius: 8px;
        padding: 14px;
        font-size: 1rem;
        font-weight: 500;
        width: 100%;
        margin-bottom: 10px;
        transition: background-color 0.3s ease;
    }

        .btn-google:hover {
            background-color: #c23321;
        }

    .btn-facebook {
        background-color: #3b5998;
        border: none;
        color: white;
        border-radius: 8px;
        padding: 14px;
        font-size: 1rem;
        font-weight: 500;
        width: 100%;
        transition: background-color 0.3s ease;
    }

        .btn-facebook:hover {
            background-color: #2d4373;
        }

    .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #0d6efd;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 10px;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .btn-loading .loading-spinner {
        display: inline-block;
    }
</style>

<div class="login-container">
    <h2>Connexion à votre compte</h2>

    @if (!string.IsNullOrEmpty(ErrorMessage))
    {
        <div class="alert alert-danger" role="alert">
            @ErrorMessage
        </div>
    }

    <form @onsubmit="HandleLogin" @onsubmit:preventDefault="true">
        <div class="mb-3">
            <label for="email" class="form-label">Adresse email</label>
            <input type="email" class="form-control" id="email" @bind="Email" placeholder="<EMAIL>">
        </div>

        <div class="mb-3">
            <label for="password" class="form-label">Mot de passe</label>
            <input type="password" class="form-control" id="password" @bind="Password" placeholder="Votre mot de passe">
        </div>

        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="rememberMe" @bind="RememberMe">
            <label class="form-check-label" for="rememberMe">
                Se souvenir de moi
            </label>
        </div>

        <button type="submit" class="btn btn-primary @(isLoading ? "btn-loading" : "")" disabled="@isLoading">
            <span class="loading-spinner"></span>
            @if (isLoading)
            {
                <span>Connexion en cours...</span>
            }
            else
            {
                <span>Se connecter</span>
            }
        </button>
    </form>

    <div class="divider">
        <span>ou</span>
    </div>

    <div class="social-login">
        <button type="button" class="btn btn-google">
            <i class="fab fa-google me-2"></i> Continuer avec Google
        </button>
        <button type="button" class="btn btn-facebook">
            <i class="fab fa-facebook-f me-2"></i> Continuer avec Facebook
        </button>
    </div>

    <div class="text-center mt-4">
        <p class="text-muted">Pas encore de compte ? <a href="/register">Créer un compte</a></p>
        <p class="text-muted"><a href="/forgot-password">Mot de passe oublié ?</a></p>
    </div>
</div>

@code {
    private string Email = "";
    private string Password = "";
    private bool RememberMe = false;
    private string ErrorMessage = "";
    private bool isLoading = false;

    private async Task HandleLogin()
    {
        isLoading = true;

        // Simple loading animation
        await Task.Delay(800);

        // Direct redirect to dashboard (no validation)
        Navigation.NavigateTo("/dashboard");
    }
}
