@page "/rapport"

<PageTitle>Rapports - Gestion des interventions</PageTitle>

<style>
    body {
        background-color: #f5f6fa;
        margin: 0;
        padding: 0;
    }

    #sidebar {
        width: 260px;
        background-color: #0d6efd;
        color: white;
        height: 100vh;
        position: fixed;
        padding-top: 2rem;
        box-shadow: 3px 0 10px rgba(0,0,0,0.15);
        display: flex;
        flex-direction: column;
        user-select: none;
        overflow-y: auto;
    }

        #sidebar .nav-link {
            color: #cbd5f0;
            font-weight: 600;
            padding: 15px 25px;
            border-left: 4px solid transparent;
            transition: background-color 0.3s, border-color 0.3s;
        }

            #sidebar .nav-link:hover {
                background-color: #0d4fbd;
                color: white;
                border-left-color: #0d6efd;
            }

            #sidebar .nav-link.active {
                background-color: #0d4fbd;
                color: white;
                border-left-color: #0d6efd;
            }

        #sidebar .submenu {
            padding-left: 1.5rem;
            font-weight: 500;
            display: none;
            flex-direction: column;
        }

            #sidebar .submenu .nav-link {
                padding: 10px 25px;
                font-size: 0.9rem;
                color: #a8bbea;
                border-left: none;
            }

                #sidebar .submenu .nav-link:hover {
                    color: #d0e1ff;
                    background-color: transparent;
                }

    .container {
        margin-left: 260px;
        padding: 40px 20px;
    }

    .card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-generate {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        border-radius: 25px;
        padding: 10px 20px;
        font-weight: 600;
        color: white;
        transition: transform 0.3s ease;
    }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

    .form-control, .form-select {
        border-radius: 8px;
        border: 1px solid #e9ecef;
        padding: 10px 15px;
    }

        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

    .table th {
        background-color: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #495057;
    }

    .table td {
        border: none;
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background-color: #f1f3f4;
    }

    .badge {
        font-size: 0.8rem;
        padding: 5px 10px;
    }

    @@media (max-width: 768px) {
        #sidebar {
            position: static;
            width: 100%;
            height: auto;
        }

        .container {
            margin-left: 0;
            padding: 20px 15px;
        }
    }
</style>

<!-- 🔹 Sidebar -->
<nav id="sidebar">
    <div class="text-center mb-4">
        <img src="images/Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white; margin-bottom: 10px;">
        <h4 style="margin: 0; font-weight: 700;">TechInterv</h4>
        <small>Logiciel d'interventions</small>
    </div>

    <a href="/dashboard" class="nav-link">Accueil</a>
    <a href="/clients" class="nav-link">Clients</a>
    <a href="/agents" class="nav-link">Agents IA</a>
    <a href="#" class="nav-link" onclick="return false;">Gestion de stock</a>
    <a href="#" class="nav-link" onclick="return false;">Mission</a>
    <a href="#" class="nav-link" onclick="return false;">Mon compte</a>

    <!-- Sous-menu réduit uniquement à "Déconnexion" -->
    <div class="submenu">
        <a href="/login" class="nav-link text-danger">
            <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
        </a>
    </div>

    <a href="/sites" class="nav-link">Sites</a>
    <a href="/rapport" class="nav-link active">Rapport</a>
</nav>

<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary mb-0">📊 Rapports d'interventions</h2>
        <button class="btn btn-success btn-generate" @onclick="GenerateReport">
            📄 Générer un rapport
        </button>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title mb-3">🔍 Filtres</h5>
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Date de début</label>
                    <input type="date" class="form-control" @bind="StartDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Date de fin</label>
                    <input type="date" class="form-control" @bind="EndDate">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Site</label>
                    <select class="form-select" @bind="SelectedSite">
                        <option value="">Tous les sites</option>
                        <option value="Usine S3">Usine S3</option>
                        <option value="Hôtel Central">Hôtel Central</option>
                        <option value="Résidence Atlas">Résidence Atlas</option>
                        <option value="Bureau Administratif">Bureau Administratif</option>
                        <option value="Clinique Santé Plus">Clinique Santé Plus</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Type</label>
                    <select class="form-select" @bind="SelectedType">
                        <option value="">Tous les types</option>
                        <option value="Maintenance">Maintenance</option>
                        <option value="Réparation">Réparation</option>
                        <option value="Installation">Installation</option>
                        <option value="Diagnostic">Diagnostic</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <button class="btn btn-primary" @onclick="FilterReports">🔍 Filtrer</button>
                    <button class="btn btn-outline-secondary ms-2" @onclick="ClearFilters">🗑️ Effacer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card p-3 shadow-sm">
                <h6 class="text-muted">Rapports générés</h6>
                <h4 class="text-primary">@TotalReports</h4>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card p-3 shadow-sm">
                <h6 class="text-muted">Interventions validées</h6>
                <h4 class="text-success">@ValidatedReports</h4>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card p-3 shadow-sm">
                <h6 class="text-muted">Rapports en attente</h6>
                <h4 class="text-warning">@PendingReports</h4>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card p-3 shadow-sm">
                <h6 class="text-muted">Documents exportés</h6>
                <h4 class="text-info">@ExportedReports</h4>
            </div>
        </div>
    </div>

    <!-- Liste des rapports -->
    <div class="card shadow-sm">
        <div class="card-body">
            <h5 class="card-title mb-4">🧾 Liste des rapports</h5>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Site</th>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Technicien</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var report in FilteredReports)
                        {
                            <tr>
                                <td>@report.Id</td>
                                <td>@report.Site</td>
                                <td>@report.Date.ToString("dd/MM/yyyy")</td>
                                <td>@report.Type</td>
                                <td>@report.Technician</td>
                                <td>
                                    <span class="badge bg-@GetStatusColor(report.Status)">@report.Status</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary me-1" @onclick="() => ViewReport(report.Id)">👁️ Voir</button>
                                    <button class="btn btn-sm btn-outline-secondary" @onclick="() => ExportToPdf(report.Id)">📥 PDF</button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@code {
    private DateTime StartDate = DateTime.Now.AddDays(-30);
    private DateTime EndDate = DateTime.Now;
    private string SelectedSite = "";
    private string SelectedType = "";

    private List<Report> Reports = new();
    private List<Report> FilteredReports = new();

    private int TotalReports => Reports.Count;
    private int ValidatedReports => Reports.Count(r => r.Status == "Validé");
    private int PendingReports => Reports.Count(r => r.Status == "En attente");
    private int ExportedReports => Reports.Count(r => r.Status == "Exporté");

    public class Report
    {
        public string Id { get; set; } = "";
        public string Site { get; set; } = "";
        public DateTime Date { get; set; }
        public string Type { get; set; } = "";
        public string Technician { get; set; } = "";
        public string Status { get; set; } = "";
        public string Description { get; set; } = "";
    }

    protected override void OnInitialized()
    {
        Reports = new List<Report>
        {
            new Report { Id = "001", Site = "Usine S3", Date = new DateTime(2025, 7, 28), Type = "Maintenance", Technician = "Ahmed Benali", Status = "Validé", Description = "Maintenance préventive des équipements" },
            new Report { Id = "002", Site = "Hôtel Central", Date = new DateTime(2025, 7, 27), Type = "Réparation", Technician = "Fatima Alami", Status = "En attente", Description = "Réparation système de climatisation" },
            new Report { Id = "003", Site = "Résidence Atlas", Date = new DateTime(2025, 7, 26), Type = "Installation", Technician = "Omar Idrissi", Status = "Validé", Description = "Installation nouvelles caméras de sécurité" },
            new Report { Id = "004", Site = "Bureau Administratif", Date = new DateTime(2025, 7, 25), Type = "Diagnostic", Technician = "Aicha Bennani", Status = "Exporté", Description = "Diagnostic réseau informatique" },
            new Report { Id = "005", Site = "Clinique Santé Plus", Date = new DateTime(2025, 7, 24), Type = "Maintenance", Technician = "Youssef Tazi", Status = "Validé", Description = "Maintenance équipements médicaux" }
        };
        FilteredReports = Reports;
    }

    private void FilterReports()
    {
        FilteredReports = Reports.Where(r =>
            (string.IsNullOrEmpty(SelectedSite) || r.Site.Contains(SelectedSite)) &&
            (string.IsNullOrEmpty(SelectedType) || r.Type == SelectedType) &&
            r.Date >= StartDate && r.Date <= EndDate
        ).ToList();
    }

    private void ClearFilters()
    {
        SelectedSite = "";
        SelectedType = "";
        StartDate = DateTime.Now.AddDays(-30);
        EndDate = DateTime.Now;
        FilteredReports = Reports;
    }

    private string GetStatusColor(string status)
    {
        return status switch
        {
            "Validé" => "success",
            "En attente" => "warning",
            "Exporté" => "info",
            _ => "secondary"
        };
    }

    private void ViewReport(string reportId)
    {
        // TODO: Implement view report functionality
    }

    private void ExportToPdf(string reportId)
    {
        // TODO: Implement PDF export functionality
    }

    private void GenerateReport()
    {
        // TODO: Implement generate report functionality
    }
}
