@page "/register"
@inject NavigationManager Navigation

<PageTitle>C<PERSON>er un compte - Logiciel d'Interventions</PageTitle>

<style>
    * {
        box-sizing: border-box;
    }

    html, body {
        height: 100%;
        margin: 0;
        font-family: 'Segoe UI', sans-serif;
        background-color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
    }

    .register-container {
        width: 100%;
        max-width: 700px;
        background-color: #fff;
        border-radius: 12px;
        padding: 40px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

        .register-container h2 {
            text-align: center;
            color: #0d6efd;
            font-weight: bold;
            margin-bottom: 30px;
        }

    .form-label {
        font-weight: 600;
        color: #333;
    }

    .form-control {
        border-radius: 8px;
        padding: 14px;
        font-size: 1rem;
        border: 1.5px solid #ccc;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 8px rgba(13, 110, 253, 0.3);
        }

    .btn-primary {
        background-color: #0d6efd;
        border: none;
        border-radius: 8px;
        padding: 14px;
        font-size: 1.1rem;
        font-weight: 600;
        width: 100%;
        transition: background-color 0.3s ease;
    }

        .btn-primary:hover {
            background-color: #0b5ed7;
        }

    .text-center a {
        color: #0d6efd;
        text-decoration: none;
        font-weight: 500;
    }

        .text-center a:hover {
            text-decoration: underline;
        }

    .alert {
        border-radius: 8px;
        font-size: 0.95rem;
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .form-check-label {
        font-size: 0.95rem;
        color: #555;
    }

    .mb-3 {
        margin-bottom: 1.5rem;
    }

    .text-muted {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #0d6efd;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 10px;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .btn-loading .loading-spinner {
        display: inline-block;
    }

    .row .col-md-6 {
        padding-right: 10px;
        padding-left: 10px;
    }

    @@media (max-width: 768px) {
        .row .col-md-6 {
            padding-right: 15px;
            padding-left: 15px;
        }
    }
</style>

<div class="register-container">
    <h2>Créer votre compte</h2>

    @if (!string.IsNullOrEmpty(ErrorMessage))
    {
        <div class="alert alert-danger" role="alert">
            @ErrorMessage
        </div>
    }

    @if (!string.IsNullOrEmpty(SuccessMessage))
    {
        <div class="alert alert-success" role="alert">
            @SuccessMessage
        </div>
    }

    <form @onsubmit="HandleRegister" @onsubmit:preventDefault="true">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="firstName" class="form-label">Prénom</label>
                    <input type="text" class="form-control" id="firstName" @bind="FirstName" placeholder="Votre prénom">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="lastName" class="form-label">Nom</label>
                    <input type="text" class="form-control" id="lastName" @bind="LastName" placeholder="Votre nom">
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label for="email" class="form-label">Adresse email</label>
            <input type="email" class="form-control" id="email" @bind="Email" placeholder="<EMAIL>">
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="password" class="form-label">Mot de passe</label>
                    <input type="password" class="form-control" id="password" @bind="Password" placeholder="Votre mot de passe">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                    <input type="password" class="form-control" id="confirmPassword" @bind="ConfirmPassword" placeholder="Confirmez votre mot de passe">
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label for="phone" class="form-label">Téléphone (optionnel)</label>
            <input type="tel" class="form-control" id="phone" @bind="Phone" placeholder="+212 6 XX XX XX XX">
        </div>

        <div class="mb-3">
            <label for="company" class="form-label">Entreprise (optionnel)</label>
            <input type="text" class="form-control" id="company" @bind="Company" placeholder="Nom de votre entreprise">
        </div>

        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="agreeTerms" @bind="AgreeTerms">
            <label class="form-check-label" for="agreeTerms">
                J'accepte les <a href="#" onclick="return false;">conditions d'utilisation</a> et la <a href="#" onclick="return false;">politique de confidentialité</a>
            </label>
        </div>

        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="newsletter" @bind="Newsletter">
            <label class="form-check-label" for="newsletter">
                Je souhaite recevoir les actualités et offres par email
            </label>
        </div>

        <button type="submit" class="btn btn-primary @(isLoading ? "btn-loading" : "")" disabled="@isLoading">
            <span class="loading-spinner"></span>
            @if (isLoading)
            {
                <span>Création en cours...</span>
            }
            else
            {
                <span>Créer mon compte</span>
            }
        </button>
    </form>

    <div class="text-center mt-4">
        <p class="text-muted">Vous avez déjà un compte ? <a href="/login">Se connecter</a></p>
    </div>
</div>

@code {
    private string FirstName = "";
    private string LastName = "";
    private string Email = "";
    private string Password = "";
    private string ConfirmPassword = "";
    private string Phone = "";
    private string Company = "";
    private bool AgreeTerms = false;
    private bool Newsletter = false;
    private string ErrorMessage = "";
    private string SuccessMessage = "";
    private bool isLoading = false;

    private async Task HandleRegister()
    {
        isLoading = true;
        ErrorMessage = "";
        SuccessMessage = "";

        // Simple loading animation
        await Task.Delay(1000);

        // Show success message briefly
        SuccessMessage = "Compte créé avec succès ! Redirection...";
        await Task.Delay(1500);

        // Direct redirect to dashboard (no validation)
        Navigation.NavigateTo("/dashboard");
    }
}
