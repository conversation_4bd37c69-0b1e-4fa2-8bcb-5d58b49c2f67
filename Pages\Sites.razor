@page "/sites"

<PageTitle>Sites - Gestion des interventions</PageTitle>

<style>
    body {
        background-color: #f5f6fa;
        margin: 0;
        padding: 0;
    }

    #sidebar {
        width: 260px;
        background-color: #0d6efd;
        color: white;
        height: 100vh;
        position: fixed;
        padding-top: 2rem;
        box-shadow: 3px 0 10px rgba(0,0,0,0.15);
        display: flex;
        flex-direction: column;
        user-select: none;
        overflow-y: auto;
    }

        #sidebar .nav-link {
            color: #cbd5f0;
            font-weight: 600;
            padding: 15px 25px;
            border-left: 4px solid transparent;
            transition: background-color 0.3s, border-color 0.3s;
        }

            #sidebar .nav-link:hover {
                background-color: #0d4fbd;
                color: white;
                border-left-color: #0d6efd;
            }

            #sidebar .nav-link.active {
                background-color: #0d4fbd;
                color: white;
                border-left-color: #0d6efd;
            }

        #sidebar .submenu {
            padding-left: 1.5rem;
            font-weight: 500;
            display: none;
            flex-direction: column;
        }

            #sidebar .submenu .nav-link {
                padding: 10px 25px;
                font-size: 0.9rem;
                color: #a8bbea;
                border-left: none;
            }

                #sidebar .submenu .nav-link:hover {
                    color: #d0e1ff;
                    background-color: transparent;
                }

    .container {
        margin-left: 260px;
        padding: 40px 20px;
    }

    .btn-add {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        border-radius: 25px;
        padding: 10px 20px;
        font-weight: 600;
        color: white;
        transition: transform 0.3s ease;
    }

        .btn-add:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

    .search-input {
        border-radius: 25px;
        padding: 12px 20px;
        border: 2px solid #e9ecef;
        font-size: 0.95rem;
        transition: border-color 0.3s ease;
    }

        .search-input:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

    .site-list {
        border-radius: 15px;
        overflow: hidden;
        max-height: 600px;
        overflow-y: auto;
    }

        .site-list .list-group-item {
            border: none;
            padding: 15px 20px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-weight: 500;
        }

            .site-list .list-group-item:hover {
                background-color: #f8f9fa;
            }

            .site-list .list-group-item.active {
                background-color: #0d6efd;
                color: white;
                border-color: #0d6efd;
            }

    .card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    iframe {
        border-radius: 12px;
    }

    @@media (max-width: 768px) {
        #sidebar {
            position: static;
            width: 100%;
            height: auto;
        }

        .container {
            margin-left: 0;
            padding: 20px 15px;
        }
    }
</style>

<!-- 🔹 Sidebar -->
<nav id="sidebar">
    <div class="text-center mb-4">
        <img src="images/Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white; margin-bottom: 10px;">
        <h4 style="margin: 0; font-weight: 700;">TechInterv</h4>
        <small>Logiciel d'interventions</small>
    </div>

    <a href="/dashboard" class="nav-link">Accueil</a>
    <a href="/clients" class="nav-link">Clients</a>
    <a href="/agents" class="nav-link">Agents IA</a>
    <a href="#" class="nav-link" onclick="return false;">Gestion de stock</a>
    <a href="#" class="nav-link" onclick="return false;">Mission</a>
    <a href="#" class="nav-link" onclick="return false;">Mon compte</a>

    <!-- Sous-menu réduit uniquement à "Déconnexion" -->
    <div class="submenu">
        <a href="/login" class="nav-link text-danger">
            <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
        </a>
    </div>

    <a href="/sites" class="nav-link active">Sites</a>
    <a href="/rapport" class="nav-link">Rapport</a>
</nav>

<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary mb-0">📍 Sites enregistrés</h2>
        <button class="btn btn-success btn-add" @onclick="AddSite">
            ➕ Ajouter un site
        </button>
    </div>

    <div class="row">
        <!-- Colonne de gauche : Liste + recherche -->
        <div class="col-md-4 mb-4">
            <input type="text" class="form-control mb-3 search-input" placeholder="🔍 Rechercher un site..." @bind="SearchTerm" @oninput="FilterSites">

            <div class="card site-list shadow-sm">
                <ul class="list-group list-group-flush">
                    @foreach (var site in FilteredSites)
                    {
                        <li class="list-group-item @(site.Id == SelectedSiteId ? "active" : "")" @onclick="() => SelectSite(site.Id)">
                            @site.Name
                        </li>
                    }
                </ul>
            </div>
        </div>

        <!-- Colonne de droite : Carte -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm p-3">
                <h5 class="mb-3">📍 Localisation du site</h5>
                @if (SelectedSite != null)
                {
                    <div class="mb-3">
                        <h6 class="text-primary">@SelectedSite.Name</h6>
                        <p class="text-muted mb-1"><i class="bi bi-geo-alt me-2"></i>@SelectedSite.Address</p>
                        <p class="text-muted mb-1"><i class="bi bi-telephone me-2"></i>@SelectedSite.Phone</p>
                        <p class="text-muted"><i class="bi bi-person me-2"></i>Contact: @SelectedSite.Contact</p>
                    </div>
                }
                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3323.4!2d-7.6!3d33.6!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzPCsDM2JzAwLjAiTiA3wrAzNicwMC4wIlc!5e0!3m2!1sen!2sma!4v1234567890"
                        width="100%"
                        height="500"
                        style="border: 0; border-radius: 8px;"
                        allowfullscreen=""
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade">
                </iframe>
            </div>
        </div>
    </div>

    <!-- Site Details -->
    @if (SelectedSite != null)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow-sm p-4">
                    <h5 class="mb-3">📋 Détails du site</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Type:</strong> @SelectedSite.Type</p>
                            <p><strong>Superficie:</strong> @SelectedSite.Area</p>
                            <p><strong>Nombre d'équipements:</strong> @SelectedSite.EquipmentCount</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Dernière intervention:</strong> @SelectedSite.LastIntervention</p>
                            <p><strong>Prochaine maintenance:</strong> @SelectedSite.NextMaintenance</p>
                            <p><strong>Statut:</strong> <span class="badge bg-@(SelectedSite.Status == "Actif" ? "success" : "warning")">@SelectedSite.Status</span></p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-primary me-2">📝 Modifier</button>
                        <button class="btn btn-info me-2">🔧 Planifier intervention</button>
                        <button class="btn btn-outline-secondary">📊 Voir historique</button>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private string SearchTerm = "";
    private int SelectedSiteId = 1;
    private List<Site> SitesList = new();
    private List<Site> FilteredSites = new();

    public class Site
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Address { get; set; } = "";
        public string Phone { get; set; } = "";
        public string Contact { get; set; } = "";
        public string Type { get; set; } = "";
        public string Area { get; set; } = "";
        public int EquipmentCount { get; set; }
        public string LastIntervention { get; set; } = "";
        public string NextMaintenance { get; set; } = "";
        public string Status { get; set; } = "";
    }

    private Site? SelectedSite => SitesList.FirstOrDefault(s => s.Id == SelectedSiteId);

    protected override void OnInitialized()
    {
        SitesList = new List<Site>
        {
            new Site { Id = 1, Name = "Usine S3 - Zone Est", Address = "Zone Industrielle, Casablanca", Phone = "+212 5 22 XX XX XX", Contact = "Ahmed Benali", Type = "Industriel", Area = "5000 m²", EquipmentCount = 25, LastIntervention = "15/07/2025", NextMaintenance = "15/08/2025", Status = "Actif" },
            new Site { Id = 2, Name = "Hôtel Central", Address = "Avenue Mohammed V, Rabat", Phone = "+212 5 37 XX XX XX", Contact = "Fatima Alami", Type = "Hôtellerie", Area = "2000 m²", EquipmentCount = 15, LastIntervention = "20/07/2025", NextMaintenance = "20/08/2025", Status = "Actif" },
            new Site { Id = 3, Name = "Résidence Atlas", Address = "Quartier Agdal, Rabat", Phone = "+212 5 37 XX XX XX", Contact = "Omar Idrissi", Type = "Résidentiel", Area = "3000 m²", EquipmentCount = 12, LastIntervention = "10/07/2025", NextMaintenance = "10/09/2025", Status = "Maintenance" },
            new Site { Id = 4, Name = "Bureau Administratif", Address = "Centre-ville, Casablanca", Phone = "+212 5 22 XX XX XX", Contact = "Aicha Bennani", Type = "Bureau", Area = "800 m²", EquipmentCount = 8, LastIntervention = "25/07/2025", NextMaintenance = "25/08/2025", Status = "Actif" },
            new Site { Id = 5, Name = "Clinique Santé Plus", Address = "Hay Riad, Rabat", Phone = "+212 5 37 XX XX XX", Contact = "Dr. Youssef Tazi", Type = "Médical", Area = "1500 m²", EquipmentCount = 20, LastIntervention = "18/07/2025", NextMaintenance = "18/08/2025", Status = "Actif" }
        };
        FilteredSites = SitesList;
    }

    private void FilterSites()
    {
        if (string.IsNullOrWhiteSpace(SearchTerm))
        {
            FilteredSites = SitesList;
        }
        else
        {
            FilteredSites = SitesList.Where(s => s.Name.Contains(SearchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
        }
    }

    private void SelectSite(int siteId)
    {
        SelectedSiteId = siteId;
    }

    private void AddSite()
    {
        // TODO: Implement add site functionality
    }
}
