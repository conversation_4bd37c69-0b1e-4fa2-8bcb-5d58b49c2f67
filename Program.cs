﻿using Syncfusion.Blazor;
using Syncfusion.Licensing;

namespace VTLeociaWebApp
{
    public class Program
    {
        public static void Main(string[] args)
        {
            // Register Syncfusion license
            SyncfusionLicenseProvider.RegisterLicense("MzkyMjE4N0AzMzMwMmUzMDJlMzAzYjMzMzAzYlltdys5MCtnSkJ4YkRvTXYyQkUzbjZSOFRwb09ENER1NHZHbjFIVW9JUU09");

            var builder = WebApplication.CreateBuilder(args);

            // Add Blazor Server services
            builder.Services.AddRazorPages();
            builder.Services.AddServerSideBlazor();

            // Add Syncfusion Blazor service
            builder.Services.AddSyncfusionBlazor();

            var app = builder.Build();

            // ✅ Permet de servir les fichiers HTML, CSS, JS, etc.
            app.UseStaticFiles();

            // Configure routing
            app.UseRouting();

            // Map Blazor Hub
            app.MapBlazorHub();
            app.MapRazorPages();
            app.MapFallbackToPage("/_Host");



            app.Run();
        }
    }
}
