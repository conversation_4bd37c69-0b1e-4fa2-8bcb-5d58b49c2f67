{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"VTLeociaWebApp/1.0.0": {"dependencies": {"Syncfusion.Blazor.Grid": "30.1.42"}, "runtime": {"VTLeociaWebApp.dll": {}}}, "Microsoft.AspNetCore.Authorization/9.0.4": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16403"}}}, "Microsoft.AspNetCore.Components/9.0.4": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.4", "Microsoft.AspNetCore.Components.Analyzers": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16403"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.4": {}, "Microsoft.AspNetCore.Components.Forms/9.0.4": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16403"}}}, "Microsoft.AspNetCore.Components.Web/9.0.4": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.4", "Microsoft.AspNetCore.Components.Forms": "9.0.4", "Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4", "Microsoft.JSInterop": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16403"}}}, "Microsoft.AspNetCore.Metadata/9.0.4": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16403"}}}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Options/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Primitives/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.JSInterop/9.0.4": {"runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16403"}}}, "Syncfusion.Blazor.Buttons/30.1.42": {"dependencies": {"Syncfusion.Blazor.Core": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Buttons.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Calendars/30.1.42": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Inputs": "30.1.42", "Syncfusion.Blazor.Lists": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Calendars.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Core/30.1.42": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.4", "Syncfusion.Licensing": "30.1.42", "System.Text.Json": "9.0.4"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Core.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Data/30.1.42": {"dependencies": {"Syncfusion.Blazor.Core": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Data.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.DropDowns/30.1.42": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42", "Syncfusion.Blazor.Inputs": "30.1.42", "Syncfusion.Blazor.Notifications": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.DropDowns.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Grid/30.1.42": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Calendars": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42", "Syncfusion.Blazor.DropDowns": "30.1.42", "Syncfusion.Blazor.Inputs": "30.1.42", "Syncfusion.Blazor.Navigations": "30.1.42", "Syncfusion.Blazor.Popups": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42", "Syncfusion.ExcelExport.Net.Core": "30.1.42", "Syncfusion.PdfExport.Net.Core": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Grids.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Inputs/30.1.42": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42", "Syncfusion.Blazor.Popups": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42", "Syncfusion.Blazor.SplitButtons": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Inputs.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Lists/30.1.42": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Lists.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Navigations/30.1.42": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42", "Syncfusion.Blazor.DropDowns": "30.1.42", "Syncfusion.Blazor.Inputs": "30.1.42", "Syncfusion.Blazor.Lists": "30.1.42", "Syncfusion.Blazor.Popups": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Navigations.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Notifications/30.1.42": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Notifications.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Popups/30.1.42": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Popups.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.Spinner/30.1.42": {"dependencies": {"Syncfusion.Blazor.Core": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Spinner.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Blazor.SplitButtons/30.1.42": {"dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Popups": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42"}, "runtime": {"lib/net9.0/Syncfusion.Blazor.SplitButtons.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.ExcelExport.Net.Core/30.1.42": {"runtime": {"lib/net9.0/Syncfusion.ExcelExport.Net.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.Licensing/30.1.42": {"runtime": {"lib/net9.0/Syncfusion.Licensing.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "Syncfusion.PdfExport.Net.Core/30.1.42": {"runtime": {"lib/net9.0/Syncfusion.PdfExport.Net.dll": {"assemblyVersion": "30.1.42.0", "fileVersion": "30.1.42.0"}}}, "System.Text.Json/9.0.4": {}}}, "libraries": {"VTLeociaWebApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-QIV3Jk/zb5wsBNkxj4eK4Qoja6f0hO2pUhWrG8NOmgnclA7zbQvzLLKaGnva7/PtglS8VS5HusZfrnnO8FTpSA==", "path": "microsoft.aspnetcore.authorization/9.0.4", "hashPath": "microsoft.aspnetcore.authorization.9.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-imeT4r1PLXqI3kzf7b06Tzj5H4od8o/u4ZE5JgyrlvwABSZlBQzWN8AgT/lPLqs2VJVxNwcAolx+vxLMl9Dn+g==", "path": "microsoft.aspnetcore.components/9.0.4", "hashPath": "microsoft.aspnetcore.components.9.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-fPrKrrfT5o0dJZ4AgbGsXBl4YNTlSrRcpYMgLTsrmYo7aOkZrdWPmsxVsFRyelfmCW9qHH7ZZK/4cTC8HuS7BA==", "path": "microsoft.aspnetcore.components.analyzers/9.0.4", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-FvoNIyz5URP2QonItKbDBzCcdmSXHWfyh0lUAYkNittlLmFAqaIF4TE55h3/SYLI8ISsbAVd44Cax86HBhLu8w==", "path": "microsoft.aspnetcore.components.forms/9.0.4", "hashPath": "microsoft.aspnetcore.components.forms.9.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-X02T/wGvT7vCgIXG0R4ZWQUx5/qMaKuWJklBL3eaa8b1NJHyPlox6IpI4AcmMy7r1hM8GwaxKfm34dv5b7n5VA==", "path": "microsoft.aspnetcore.components.web/9.0.4", "hashPath": "microsoft.aspnetcore.components.web.9.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-3AhnSEW/Zw0E++itsen8YvqT7UoyF7zYD9OZPQmeGpT7YQP4W9GOiwOljfJEC8+4pP8kqgpeRN/p4LkmKLIUlQ==", "path": "microsoft.aspnetcore.metadata/9.0.4", "hashPath": "microsoft.aspnetcore.metadata.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-f2MTUaS2EQ3lX4325ytPAISZqgBfXmY0WvgD80ji6Z20AoDNiCESxsqo6mFRwHJD/jfVKRw9FsW6+86gNre3ug==", "path": "microsoft.extensions.dependencyinjection/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "path": "microsoft.extensions.logging.abstractions/9.0.4", "hashPath": "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "path": "microsoft.extensions.options/9.0.4", "hashPath": "microsoft.extensions.options.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "path": "microsoft.extensions.primitives/9.0.4", "hashPath": "microsoft.extensions.primitives.9.0.4.nupkg.sha512"}, "Microsoft.JSInterop/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-iuzwm79XoRYTRhf7cOHgMgGAE8ZsAcCGp2VsIDAV05qRzceKSE1S7tQp+8GTzG7+IZpdPpvfaJUwSD/pYIog5A==", "path": "microsoft.jsinterop/9.0.4", "hashPath": "microsoft.jsinterop.9.0.4.nupkg.sha512"}, "Syncfusion.Blazor.Buttons/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-O1HUjjQ0F7zI4lbMSdhw1DRv1KoSZhaU0L8MgDIrN1I24NX7DRAGOkCpa6vJ7uggD4PiTrYPxNcT1kaUFAH+nA==", "path": "syncfusion.blazor.buttons/30.1.42", "hashPath": "syncfusion.blazor.buttons.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Calendars/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-sq8ZGQovVK+0gsM59uQFd451sUC0Lw/figjfNEqK8S8xSFDzz3zVkkdje02SnT+aFItA8BVwgDY+/DVdVk/CGQ==", "path": "syncfusion.blazor.calendars/30.1.42", "hashPath": "syncfusion.blazor.calendars.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Core/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-OCUL7r2B+904tjPsKFKzi5F0qF0nkJu9EgF4JSVwLlAE1AbkjePvyZo4fJavAPwDHiPQKV6hChjWGFYBr4HOqw==", "path": "syncfusion.blazor.core/30.1.42", "hashPath": "syncfusion.blazor.core.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Data/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-5ZvQq1XqyZgbGFCQeomuwQlkYlYl6q14QVEN+pFYTIXjYhogtVxczL2i3CO2BYTBHnP+h3A9LHQ5cxeDyDNVAA==", "path": "syncfusion.blazor.data/30.1.42", "hashPath": "syncfusion.blazor.data.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.DropDowns/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-3dN2N/rYgneYV/U5bljprcamNdeuQIS/7zgzRScMdsTpZmBxVbNyNWTUXWiuRfsZIpBdsrHxG0WDXxyzYepF/Q==", "path": "syncfusion.blazor.dropdowns/30.1.42", "hashPath": "syncfusion.blazor.dropdowns.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Grid/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-nWMjeCUtvr6+Z+3sAaKV01z7/lOjkZ+jZvAYqZLIHg3qJuLO2xxw8BSUzHKibHZT7Oab7BvzVyMbQwphnxPq2w==", "path": "syncfusion.blazor.grid/30.1.42", "hashPath": "syncfusion.blazor.grid.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Inputs/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-5YLD/7Q+U9k107gnbbrf1riB/Krc6YgLyvFrTQkOHQdtvQLEimAjsVbOv1Yd3iL2dI7hJS/HdxxSuGScHPKG7w==", "path": "syncfusion.blazor.inputs/30.1.42", "hashPath": "syncfusion.blazor.inputs.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Lists/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-n+d/KbBHXaB7f/dh4UHb6xOURmAQI8Bn1T2z4GTcNXttHcudxtmKIpOD0xKegfzsGXUS9m/e4dNQ13/HT3ZupA==", "path": "syncfusion.blazor.lists/30.1.42", "hashPath": "syncfusion.blazor.lists.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Navigations/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-BgURjGgAF0kPfYGHoN6uvHS/CB0RhMUaWTtXO8l/iL3Duuan9R68rN2VhxmRSmJNa7aG34xkw2PcF+i0RP7SRA==", "path": "syncfusion.blazor.navigations/30.1.42", "hashPath": "syncfusion.blazor.navigations.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Notifications/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-eWglrkehhHICAO19g6GCNzixShq+80oqk5y17GJcYHNJ+c/YzGlrRZI9a4ZM8iGldYKEo8HtjKLc2jZyULBpwg==", "path": "syncfusion.blazor.notifications/30.1.42", "hashPath": "syncfusion.blazor.notifications.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Popups/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-7OvjMizvioR9AwKwBbyqGLeWd++5oOlc47v5+6sne0RQRp8oZgDJy5Sgf7cUkRFXpZcdRhi4DjLPMplS7OPtwg==", "path": "syncfusion.blazor.popups/30.1.42", "hashPath": "syncfusion.blazor.popups.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.Spinner/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-gezwhFfER1tnszBE644mohK09tLJnbjED4nBvLkRsR/UYxhyTu1lhLVAmPxNwxfHbDguH4rsgMk8YcsYHtPK/A==", "path": "syncfusion.blazor.spinner/30.1.42", "hashPath": "syncfusion.blazor.spinner.30.1.42.nupkg.sha512"}, "Syncfusion.Blazor.SplitButtons/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-72HehyaOii+NtkBNaK+xYYsofpBebFYQHW2q6Ub7F4+NdCJZEFsjZXZzd2UXSphd3MDH0RZKMeBrgz0lAb071A==", "path": "syncfusion.blazor.splitbuttons/30.1.42", "hashPath": "syncfusion.blazor.splitbuttons.30.1.42.nupkg.sha512"}, "Syncfusion.ExcelExport.Net.Core/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-dPETqUtSeP2+gpMdVtCwlh284fqX6sy6GRBDoh6Cob+QZv3PV5bNq2NbUAwnBPpV+jhynspagczEn6x72xrNGQ==", "path": "syncfusion.excelexport.net.core/30.1.42", "hashPath": "syncfusion.excelexport.net.core.30.1.42.nupkg.sha512"}, "Syncfusion.Licensing/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-ykLfYHAplHfljY5Iq1P5hzeF+dgGnfU/BfmU/z7rvk6SszSTM/Lkub1PO1eqpZdXhx+symcxbHHe5j7P7HyfBA==", "path": "syncfusion.licensing/30.1.42", "hashPath": "syncfusion.licensing.30.1.42.nupkg.sha512"}, "Syncfusion.PdfExport.Net.Core/30.1.42": {"type": "package", "serviceable": true, "sha512": "sha512-h3vX39eSEGJzBjZLDVp0h1vdhh/4L2f7W38gDMeORmROTWsroagVvYtsJPZsVHcPTPrs7HkZQlKXDbCWS6gr8Q==", "path": "syncfusion.pdfexport.net.core/30.1.42", "hashPath": "syncfusion.pdfexport.net.core.30.1.42.nupkg.sha512"}, "System.Text.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-pYtmpcO6R3Ef1XilZEHgXP2xBPVORbYEzRP7dl0IAAbN8Dm+kfwio8aCKle97rAWXOExr292MuxWYurIuwN62g==", "path": "system.text.json/9.0.4", "hashPath": "system.text.json.9.0.4.nupkg.sha512"}}}