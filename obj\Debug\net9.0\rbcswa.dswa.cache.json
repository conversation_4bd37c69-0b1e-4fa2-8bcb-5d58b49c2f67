{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["yKXolq4DgF8/WPx/WmvTcGA863hQb/R46jtBBajABIU=", "DOt4yfSqkK84yUu8b7ICs4ja9r9iC07MPX+PO+44M7c=", "bFE+m1f9Mo3BTNomgVcu9skMxvBD9ZgUnhghSSvnmpk=", "N7UWOj3Oz6qHsLm/6/Iapl8B175S6VwG01hK8KJV6I0=", "zwmhnobw51x46BP7WlDjMTfN57dXyJdTIfTGwoIdHMY=", "ggLGLbEikz18PCiwfhtc+WwJOXAg0yN3GD8HvGRApeU=", "NLnprxQ2CnxTUWQOBTgR8HTT3NKZWdqRKAiGLr5UrTg=", "MLCu89bJWxDe9wPH8KhuKdaVLmmW4oNku1tTOjsuXZc=", "apDfgSzA6BSAQC/XwyO03a3YZsEM8CRPytO79vgqnIE=", "cfejl83HPy65pCOJjiJeytovOYgGpUghFY2uWVn8iwY=", "yzmQc2ByI3IX9TL3wbFqa/yX6yuL99HFAp8PXC8GbK0=", "cA4ln8hInoNxJ49m+wlG3VufNCuA7SjgpKbzH9Q/+gM=", "niJpwa8hLvfO8D2eYvWwxpBi6ZWupnyMk9CVYHm8reI=", "cqRHrgFM52hLUgi4FgLOLeaGRG36/Et6EvGMD/ZakrE=", "x712CLFwHWXeHkzGrhkc9DiyPO2A9CID21Qokb1Obw8=", "d4oKxtZk5zRfol3wItVtAzS+CFZGi6NBrvu/OZJj0cU=", "ypw7lzSc9RC8Ew2mmmgDi9edX3cGykd+hY19Lfy8gP4=", "z1oxHe5WXIRHudrokXFXZ+D2bzFLzc2XiwXZx1SvA4U=", "w1HOF+mmHzbSlj5jvjpZ2yESzhcoPSNLg5QBYsCcwj0=", "8NdFfkwG4SoAqOUtTzfRgCgnQGNsWpzjM+VGYceAATk=", "AW+LNbMdItFnBekmwxwMq/dTflUaMD/4PWfnPK5T+cQ=", "lzMTKptWM3A5lJ43Njq/u81260PFK+eM5JsMe8cyzMQ=", "mO72MkloAktCUbNGPIqIi+6HPbnDj1XvmsIIsD7XMZc=", "a6TxZb9UGICR2DcUvih+vQ80sM3SdJF4f+kP8NS7WM0=", "sOrByTQZKuQ81LtBy/dVkioHV9hUzFSYCKrtN8/0NrM=", "ZJOQvqjNbOKtV7S+0Ur8Jky6n/cdpyLwbo9bp4LZkiI=", "Q+HxIGUKQxVUgcvriYvQNpTCnEbgdslOztM9kY9XpKs=", "29K05ApeFQxVn0Kr+VO3BjNFo9YW3fkrrH05w/SPqNs=", "YWerQO1iDwLj0Zaj/4XtB2eh9LfwszDUWOZ9F6cjxaU=", "mN440FENHbrj6xXVvlXSoZLNWc4Ntn2p2/vzTaqYbj0=", "cK9/pwDDwsDLHEKsqi+5wkhVIxCPksLS1TXl2F6vzak=", "MgLVuHyG4quaXCn6YbAB/wjeG6ZA5hQIQfpWn3jOYmc=", "iwrlFDgvK5E+TnpC2MpLKyZi0Wb3gvKvlJF7vgaf1do=", "EV2vwScSLOpmFtgz73R+GQpyizW8rrHZCdEtAcg3bc0=", "UAW0TKJhR0UgTy0eToOHNsvWSiNqSb+kxB6jC+M/sr8=", "m6Hq8xjgqcCfKYZp03RI5cW1saQXZoZTfM387xTa+ME=", "zDTcr65UBfNTJt2WpiPDaNxZL/Ryr/XD+63hMLd7MUo=", "E3DHyoKkQTOxpd/aEfXym2Maxiurk9L2uhARPxCpe4w=", "H5J/04GBypATPlpQcI0CpLdRRQCRZlhJiToe7i8D/hU=", "aEZ9hovU/aNgAjG/H8I9wMmbINM5VPpssJ00Xd6NECY=", "X00LpXejRuGgCI7+NMs36tyad1/j599vAsobHOKQFG0=", "JV+5KlTJVqR1tCGcqVGPFhvamS5g6e8R/eqQv/oGM+E=", "VDcZR/h6Plu2RaaPxFzgAX2/jTbj9JhgvHNiofgHbPc=", "d3+DyS4lsNf5p8C6IPtDzlmsUYDL4DNR8X+E1MyzfYU=", "Pzn7Gz7uO7g3WTvIOgBJA16m0U/yw98wD9Z6CTg8FBI=", "oAHEcwmq8ws0j4ZBSadMW7t5HoTSe5y1v2FP4DYtJ/8=", "Bnxpd01U3nYE9xDKRIVbmayxdZa+xhbTTfugvMLs+X4=", "M8wU29yLhg6FbENXjDWInrZvZywYNtVksp/J4mCii0M=", "pBbYCWs+sCruBf47PRcMaiAORrL4yIQF1Wr4/p2BjgA=", "VNw+ED+EmBfJnw7ncfSRx49FMVlrY1jdXjfHPFVPS1E=", "wi76GTEL2nhq5tTXOZhhCxfkAaZ5Lr7Fd8S2yexcGAM=", "GDrwwAapHDV97Xos4oLexP5bL6pepdX1gVS2n3xaT2Y=", "4MQ+NFAXiGHWHq/nKwvuK1GfGiwuo6Kg7GNoWChlFfw=", "0P4NhJVA2I7gbzzWwwt8DQEMgG17yVeTH/lOf1yXfh4=", "nYI8DXREa3adShVX38n5XygK8JhGmVpDt5BJjVsKqsE=", "F03A3YVjhEk2aPccGq7gyDxE9w3EKu9hcf3cT3HOKao=", "VtAQ74NSx76uj6qkJ8saBtut2/9RqviMNrCONiF+SrY=", "bhhHjtezWGJ55nLfUqTijg4JN1na7bQKsxbjKRcrxU8=", "p56qkZhXUaxXPf6b1DGyhQGOgN8lHgDVX4WmZUyqeXw=", "ATPapzIZ77xiCzlzbhXlQamFXWC6ER2EvbaBpbXOMi4=", "myvyTWSFMwx19eMgXphDZgm1sG2ZRhVmy0YVb4oY4eU=", "2E+NY4ucXcNjdTZFol4kmgsNFLaGTy3JCRJpjQqxuq0=", "7r2o6YUcrWR1WHH4vXdE1HK0kFEcfoPkF1wgaHznSzM=", "XRUGI+9Y6SV55Hal4Cg73xzQuq4bRKOOzHF/P9ZdwJk="], "CachedAssets": {"XRUGI+9Y6SV55Hal4Cg73xzQuq4bRKOOzHF/P9ZdwJk=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2dj3icwxz-pbnxt20j60.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Clients#[.{fingerprint=pbnxt20j60}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Clients.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b5uynd6xpu", "Integrity": "4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Clients.html", "FileLength": 2226, "LastWriteTime": "2025-07-31T10:21:58.25495+00:00"}, "7r2o6YUcrWR1WHH4vXdE1HK0kFEcfoPkF1wgaHznSzM=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gyys9ii1lu-ot006j5kmx.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Dashboard#[.{fingerprint=ot006j5kmx}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Dashboard.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vmeviq3rrr", "Integrity": "KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Dashboard.html", "FileLength": 5676, "LastWriteTime": "2025-07-31T10:21:58.2569364+00:00"}, "myvyTWSFMwx19eMgXphDZgm1sG2ZRhVmy0YVb4oY4eU=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dtw28fmir4-wl6n5gvn3c.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Gestion de stock#[.{fingerprint=wl6n5gvn3c}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Gestion de stock.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q4sc7o4ypy", "Integrity": "ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Gestion de stock.html", "FileLength": 2585, "LastWriteTime": "2025-07-31T10:21:58.25495+00:00"}, "ATPapzIZ77xiCzlzbhXlQamFXWC6ER2EvbaBpbXOMi4=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\yb1j5ju6dz-o0kmq4r1qc.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Home#[.{fingerprint=o0kmq4r1qc}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Home.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t7qn3zznmz", "Integrity": "h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Home.html", "FileLength": 5073, "LastWriteTime": "2025-07-31T10:21:58.255995+00:00"}, "p56qkZhXUaxXPf6b1DGyhQGOgN8lHgDVX4WmZUyqeXw=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\od31b7jcnl-xqeb31wrq2.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Interventions#[.{fingerprint=xqeb31wrq2}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Interventions.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uxukq9t0b8", "Integrity": "osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Interventions.html", "FileLength": 1639, "LastWriteTime": "2025-07-31T10:21:58.25495+00:00"}, "bhhHjtezWGJ55nLfUqTijg4JN1na7bQKsxbjKRcrxU8=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\90k8s08yp3-7xmio610hh.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Login#[.{fingerprint=7xmio610hh}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Login.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2s7p1y7a10", "Integrity": "okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Login.html", "FileLength": 1570, "LastWriteTime": "2025-07-31T10:21:58.255995+00:00"}, "VtAQ74NSx76uj6qkJ8saBtut2/9RqviMNrCONiF+SrY=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ji856uyn4d-2k64m4xzz5.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Mission#[.{fingerprint=2k64m4xzz5}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Mission.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "th9ozykzb6", "Integrity": "EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Mission.html", "FileLength": 3311, "LastWriteTime": "2025-07-31T10:21:58.25495+00:00"}, "F03A3YVjhEk2aPccGq7gyDxE9w3EKu9hcf3cT3HOKao=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2pse2bi12y-vg9bitalgh.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "MonCompte#[.{fingerprint=vg9bitalgh}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\MonCompte.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ax83nn60c7", "Integrity": "7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\MonCompte.html", "FileLength": 2340, "LastWriteTime": "2025-07-31T10:21:58.255995+00:00"}, "nYI8DXREa3adShVX38n5XygK8JhGmVpDt5BJjVsKqsE=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5a6tfv8f1g-pdyi0fsnlt.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Rapport#[.{fingerprint=pdyi0fsnlt}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Rapport.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ewll9l7nnb", "Integrity": "Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Rapport.html", "FileLength": 2168, "LastWriteTime": "2025-07-31T10:21:58.25495+00:00"}, "0P4NhJVA2I7gbzzWwwt8DQEMgG17yVeTH/lOf1yXfh4=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\oz70jm6r81-m6a14prokw.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Register#[.{fingerprint=m6a14prokw}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Register.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b7od6h08qt", "Integrity": "x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Register.html", "FileLength": 1938, "LastWriteTime": "2025-07-31T10:21:58.2569364+00:00"}, "4MQ+NFAXiGHWHq/nKwvuK1GfGiwuo6Kg7GNoWChlFfw=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\cfe1nezmlr-gs3mxpddy3.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Sites#[.{fingerprint=gs3mxpddy3}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Sites.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tmfbkvgug7", "Integrity": "G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Sites.html", "FileLength": 2544, "LastWriteTime": "2025-07-31T10:21:58.2569364+00:00"}, "GDrwwAapHDV97Xos4oLexP5bL6pepdX1gVS2n3xaT2Y=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4nl7ma3ohr-kjm711ejyc.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Style#[.{fingerprint=kjm711ejyc}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3jyu6p499j", "Integrity": "T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Style.css", "FileLength": 430, "LastWriteTime": "2025-07-31T13:51:57.1145254+00:00"}, "2E+NY4ucXcNjdTZFol4kmgsNFLaGTy3JCRJpjQqxuq0=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\1nafdd6chw-tgup6kq3m2.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "favicon#[.{fingerprint=tgup6kq3m2}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7lue81jric", "Integrity": "pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\favicon.ico", "FileLength": 80483, "LastWriteTime": "2025-07-31T13:37:02.4010277+00:00"}, "yKXolq4DgF8/WPx/WmvTcGA863hQb/R46jtBBajABIU=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\d77l2qy8cy-buyzacakm8.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/popup.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popup.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7h1es5aypm", "Integrity": "BRHnOK8U7cDSZgPiaN1GF6MWPxMxONHqj2q8eGzakuM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popup.min.js", "FileLength": 4243, "LastWriteTime": "2025-07-31T14:12:34.2190201+00:00"}, "DOt4yfSqkK84yUu8b7ICs4ja9r9iC07MPX+PO+44M7c=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\kg2ram854o-mzeb2kh2gi.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/popupsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popupsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nnejunttvj", "Integrity": "DYW/5c69o8fCUolvnIH4ijloP1Qu6US5SijIu0Cbr84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popupsbase.min.js", "FileLength": 4448, "LastWriteTime": "2025-07-31T14:12:34.2110417+00:00"}, "bFE+m1f9Mo3BTNomgVcu9skMxvBD9ZgUnhghSSvnmpk=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\idlbwfjf3e-jkhlsawj7m.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/sf-svg-export.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\sf-svg-export.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o4c6chspmg", "Integrity": "6PKsxj/uvLNsJih81tS2vn15rG4zLiEJcLp7rz6odEw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\sf-svg-export.min.js", "FileLength": 3687, "LastWriteTime": "2025-07-31T14:12:34.2151479+00:00"}, "N7UWOj3Oz6qHsLm/6/Iapl8B175S6VwG01hK8KJV6I0=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ckt86gem57-1jog2vdvqp.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/svgbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\svgbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilm4r305hc", "Integrity": "ZnKvdEdeBNgXF4UWgw0ZEusbJpvdvSguVZ882RlZWrU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\svgbase.min.js", "FileLength": 12776, "LastWriteTime": "2025-07-31T14:12:34.2221134+00:00"}, "zwmhnobw51x46BP7WlDjMTfN57dXyJdTIfTGwoIdHMY=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dnwq9wk44u-ri75jdaj3r.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/syncfusion-blazor-base.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yyq1vnkanj", "Integrity": "yjn/H+D9FW90HM2T05OHIbPgyhlHB+D6hWUn3L9s9Ds=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "FileLength": 102137, "LastWriteTime": "2025-07-31T14:12:34.2489553+00:00"}, "ggLGLbEikz18PCiwfhtc+WwJOXAg0yN3GD8HvGRApeU=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5h81voagx1-by0shsu0jg.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/syncfusion-blazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xbwu5l4tcp", "Integrity": "0+fg4riJa17YLHW0axaWYAnGEwnJQG79HosDok95qtw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "FileLength": 990700, "LastWriteTime": "2025-07-31T14:12:34.434447+00:00"}, "NLnprxQ2CnxTUWQOBTgR8HTT3NKZWdqRKAiGLr5UrTg=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2ndbdiwf0k-jqs3n24tf7.gz", "SourceId": "Syncfusion.Blazor.Spinner", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Spinner", "RelativePath": "scripts/sf-spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\sf-spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sewm4nhubs", "Integrity": "bOLNw1bgUPqHPOUK4WRLzDzyHxgE6es4bGpjBwfjTw8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\sf-spinner.min.js", "FileLength": 471, "LastWriteTime": "2025-07-31T14:12:34.4374364+00:00"}, "MLCu89bJWxDe9wPH8KhuKdaVLmmW4oNku1tTOjsuXZc=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\g591birpkc-4h59cheqig.gz", "SourceId": "Syncfusion.Blazor.Spinner", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Spinner", "RelativePath": "scripts/spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "queyjsmx0k", "Integrity": "Tv00ASBTMUGgKKFdiZ0djWPDf6VTnpz7xusIVckSIkc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\spinner.min.js", "FileLength": 3384, "LastWriteTime": "2025-07-31T14:12:34.4405674+00:00"}, "apDfgSzA6BSAQC/XwyO03a3YZsEM8CRPytO79vgqnIE=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ha3ck1f4wq-0ir6ibxc9a.gz", "SourceId": "Syncfusion.Blazor.Buttons", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Buttons", "RelativePath": "scripts/sf-floating-action-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcynizji7r", "Integrity": "VYRLnTV2bt78z6Mvm/iSjeHfsQlRJ6warRKTBRzJTVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "FileLength": 754, "LastWriteTime": "2025-07-31T14:12:34.20406+00:00"}, "cfejl83HPy65pCOJjiJeytovOYgGpUghFY2uWVn8iwY=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\onzwzcnqot-d2wfjv2vus.gz", "SourceId": "Syncfusion.Blazor.Buttons", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Buttons", "RelativePath": "scripts/sf-speeddial.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-speeddial.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpt0x5qokx", "Integrity": "l9T25TTVRJDMsQ37KXGjrgqHFV/HSSsq9Qty1ZJIG1k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-speeddial.min.js", "FileLength": 2521, "LastWriteTime": "2025-07-31T14:12:34.2180228+00:00"}, "yzmQc2ByI3IX9TL3wbFqa/yX6yuL99HFAp8PXC8GbK0=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2lc9f2kwg-xddooubi4o.gz", "SourceId": "Syncfusion.Blazor.Popups", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Popups", "RelativePath": "scripts/sf-dialog.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-dialog.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ueqpgxf806", "Integrity": "9nV3uGup69tcE0TavB1jPfRIaUh5AIzerTrukcCqLfg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-dialog.min.js", "FileLength": 5743, "LastWriteTime": "2025-07-31T14:12:34.2319852+00:00"}, "cA4ln8hInoNxJ49m+wlG3VufNCuA7SjgpKbzH9Q/+gM=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\3spo2eianh-e2u6q3p1lh.gz", "SourceId": "Syncfusion.Blazor.Popups", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Popups", "RelativePath": "scripts/sf-tooltip.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-tooltip.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pbv6q1n0a2", "Integrity": "663pRccky4jzFU7mEhDEruVWQwL74Cq7hAgO+jjHzsc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-tooltip.min.js", "FileLength": 7306, "LastWriteTime": "2025-07-31T14:12:34.2200183+00:00"}, "niJpwa8hLvfO8D2eYvWwxpBi6ZWupnyMk9CVYHm8reI=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\bm0n96jk7v-y4pke33q7n.gz", "SourceId": "Syncfusion.Blazor.SplitButtons", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.SplitButtons", "RelativePath": "scripts/sf-drop-down-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d1hgg1ijyp", "Integrity": "QU7kJBL8MSk41KRAYHPDylFkQgmBSTRJXxXvDo8VF/I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "FileLength": 2510, "LastWriteTime": "2025-07-31T14:12:34.2301382+00:00"}, "cqRHrgFM52hLUgi4FgLOLeaGRG36/Et6EvGMD/ZakrE=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\p2zgtah1ds-aphbptuiu1.gz", "SourceId": "Syncfusion.Blazor.SplitButtons", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.SplitButtons", "RelativePath": "scripts/splitbuttonsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b36h10ukux", "Integrity": "6UU37OjaY6/vrGU+s3xH0DO1ehrN+ZyLdS5we1JzE6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "FileLength": 1446, "LastWriteTime": "2025-07-31T14:12:34.2250923+00:00"}, "x712CLFwHWXeHkzGrhkc9DiyPO2A9CID21Qokb1Obw8=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\9zg8dqxfdk-viv3nqn9op.gz", "SourceId": "Syncfusion.Blazor.Notifications", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Notifications", "RelativePath": "scripts/sf-toast.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\staticwebassets\\scripts\\sf-toast.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2adsh06qhk", "Integrity": "OsMVazHqMjAjwfwXwli8LGa6lwXPJCyNsd5gKONG088=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\staticwebassets\\scripts\\sf-toast.min.js", "FileLength": 2216, "LastWriteTime": "2025-07-31T14:12:34.2379693+00:00"}, "d4oKxtZk5zRfol3wItVtAzS+CFZGi6NBrvu/OZJj0cU=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\lc6ok7c9tb-k2m5n6fmft.gz", "SourceId": "Syncfusion.Blazor.Data", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Data", "RelativePath": "scripts/data.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\staticwebassets\\scripts\\data.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lrloyer4sr", "Integrity": "z3Fa5jflZwuv7LLQxMSoLo/kuxF59w0qjMIwJQwOn+Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\staticwebassets\\scripts\\data.min.js", "FileLength": 25024, "LastWriteTime": "2025-07-31T14:12:34.2579173+00:00"}, "ypw7lzSc9RC8Ew2mmmgDi9edX3cGykd+hY19Lfy8gP4=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4l15mon31j-m9eje2li2o.gz", "SourceId": "Syncfusion.Blazor.Lists", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Lists", "RelativePath": "scripts/sf-listview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\staticwebassets\\scripts\\sf-listview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t7zi9sg0mf", "Integrity": "yRtRpRHENr0dEYS5sUrDbfJeJ/yMAh+lhgkzRenIzSY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\staticwebassets\\scripts\\sf-listview.min.js", "FileLength": 5171, "LastWriteTime": "2025-07-31T14:12:34.2110417+00:00"}, "z1oxHe5WXIRHudrokXFXZ+D2bzFLzc2XiwXZx1SvA4U=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jsshet654m-bdnhf5zdvw.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-colorpicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-colorpicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w59p7vvrse", "Integrity": "XBJZ5jbyuiWhgMIupg52r9BTMqCnMyRs253sr9Mndjs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-colorpicker.min.js", "FileLength": 1936, "LastWriteTime": "2025-07-31T14:12:34.2180228+00:00"}, "w1HOF+mmHzbSlj5jvjpZ2yESzhcoPSNLg5QBYsCcwj0=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\6wjchtuxco-u8jmjqbpf2.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-maskedtextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6u98uofk2b", "Integrity": "zhFDXLiucb0jw5ocM5WBfmYO56xBkyhOvrdLEI4gcaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "FileLength": 2566, "LastWriteTime": "2025-07-31T14:12:34.2240077+00:00"}, "8NdFfkwG4SoAqOUtTzfRgCgnQGNsWpzjM+VGYceAATk=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sadf1bszs1-fjcbrnxrtb.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-numerictextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n9rdkaa0r5", "Integrity": "S2eq5BRVdDweA8Qh8cqQpTyaS3y0YxICEfj3YmsnEMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "FileLength": 3036, "LastWriteTime": "2025-07-31T14:12:34.2330484+00:00"}, "AW+LNbMdItFnBekmwxwMq/dTflUaMD/4PWfnPK5T+cQ=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\12khajnlde-cg29fk2gn2.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-otp-input.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-otp-input.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rykoqi2klb", "Integrity": "I4KnvqwCsqAis1iU0aCb0LOTcgdZY7RbtjRtdbiVvEs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-otp-input.min.js", "FileLength": 948, "LastWriteTime": "2025-07-31T14:12:34.2489553+00:00"}, "lzMTKptWM3A5lJ43Njq/u81260PFK+eM5JsMe8cyzMQ=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\vbtp9tnch6-vteibwr79c.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-rating.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-rating.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5156q33gia", "Integrity": "+dQJCfeGGJoL1/ZtpznF0DjWjmv9HNWHuZghME+P8yI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-rating.min.js", "FileLength": 2281, "LastWriteTime": "2025-07-31T14:12:34.2635334+00:00"}, "mO72MkloAktCUbNGPIqIi+6HPbnDj1XvmsIIsD7XMZc=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sb4o3odzm8-x0gcv3gb9d.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-signature.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-signature.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tqw1ix8j0f", "Integrity": "BRJhIImstd4aM5hz+Qf0vpcz4ad7EIzblxrXY36nEXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-signature.min.js", "FileLength": 5751, "LastWriteTime": "2025-07-31T14:12:34.2739285+00:00"}, "a6TxZb9UGICR2DcUvih+vQ80sM3SdJF4f+kP8NS7WM0=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jazegy34qu-lsufkzl165.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-slider.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-slider.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1epy3zj0bg", "Integrity": "98tM+/ygumT0DfanDWUi+Ev/kIr7s9BK8v2BEPJYS/Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-slider.min.js", "FileLength": 6225, "LastWriteTime": "2025-07-31T14:12:34.2419586+00:00"}, "sOrByTQZKuQ81LtBy/dVkioHV9hUzFSYCKrtN8/0NrM=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\kn04c7n2x3-g9zntctz7a.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-speechtotext.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-speechtotext.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7e0quenm8l", "Integrity": "gbVH8zeaCoboxjY1YJN1D0r3ZqCJY45dNiw9hm/RY7I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-speechtotext.min.js", "FileLength": 905, "LastWriteTime": "2025-07-31T14:12:34.2082513+00:00"}, "ZJOQvqjNbOKtV7S+0Ur8Jky6n/cdpyLwbo9bp4LZkiI=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ffhxaeu1yb-j3z817ikhe.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-textarea.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textarea.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n6dae95oz5", "Integrity": "P0Omx/MN0b5JoBSlcuJ4hegPFxyPLBgwwnauBQShyIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textarea.min.js", "FileLength": 606, "LastWriteTime": "2025-07-31T14:12:34.2160282+00:00"}, "Q+HxIGUKQxVUgcvriYvQNpTCnEbgdslOztM9kY9XpKs=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\pq6ds00kew-x6kv5h1gyc.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-textbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n77thzgy20", "Integrity": "PZn71QUo0qDByztlZYFVepsJWrX+ux3ZQDfQRf5QaHU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textbox.min.js", "FileLength": 1091, "LastWriteTime": "2025-07-31T14:12:34.2282614+00:00"}, "29K05ApeFQxVn0Kr+VO3BjNFo9YW3fkrrH05w/SPqNs=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gk0sdbowrt-1zm61t9neq.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-uploader.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-uploader.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1n2zprm7ma", "Integrity": "4+L5ZLoTRFvukhCazVyEv9ffYYUHNkaCHJQGYUzThu8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-uploader.min.js", "FileLength": 19042, "LastWriteTime": "2025-07-31T14:12:34.2475459+00:00"}, "YWerQO1iDwLj0Zaj/4XtB2eh9LfwszDUWOZ9F6cjxaU=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sekkta200r-67586omov2.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-dropdownlist.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wr3od8<PERSON>oy", "Integrity": "HOZ8mWf74M0BAkiCAiIOqbO3gp6K+L6ye3QocXOX098=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "FileLength": 9661, "LastWriteTime": "2025-07-31T14:12:34.2549243+00:00"}, "mN440FENHbrj6xXVvlXSoZLNWc4Ntn2p2/vzTaqYbj0=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\0yyctx1peb-9j6p65atja.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-listbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-listbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u1q8ipkat6", "Integrity": "4ku+jnyI4WcPVS0hujY7O0pF/UOm/+jv9WtB1klpd2o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-listbox.min.js", "FileLength": 1907, "LastWriteTime": "2025-07-31T14:12:34.2669575+00:00"}, "cK9/pwDDwsDLHEKsqi+5wkhVIxCPksLS1TXl2F6vzak=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\eiq2m65tqw-4hzxl1uzz6.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-mention.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-mention.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "94kimzvjjt", "Integrity": "WSje0yskblEFm2pjNYaF8ed1MQ/A0DCScJkoSA585EE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-mention.min.js", "FileLength": 5781, "LastWriteTime": "2025-07-31T14:12:34.2861634+00:00"}, "MgLVuHyG4quaXCn6YbAB/wjeG6ZA5hQIQfpWn3jOYmc=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\juc69m01qj-wdnlhme2ar.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-multiselect.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-multiselect.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c77qxgggi1", "Integrity": "CFiKuIrnBTD3hRTjmOoWnmPPoXUugxZwQe5vlsZGXgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-multiselect.min.js", "FileLength": 8469, "LastWriteTime": "2025-07-31T14:12:34.2529295+00:00"}, "iwrlFDgvK5E+TnpC2MpLKyZi0Wb3gvKvlJF7vgaf1do=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\anji1rpska-ykoprjbkzz.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sortable.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sortable.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zcw6j3ed9t", "Integrity": "rRdLa/O0a/f4QZ55ICih3BZ0etmwyr2s2gIrIpWGmtI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sortable.min.js", "FileLength": 3148, "LastWriteTime": "2025-07-31T14:12:34.2051378+00:00"}, "EV2vwScSLOpmFtgz73R+GQpyizW8rrHZCdEtAcg3bc0=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\vihrpo2kci-sm61wjcwzb.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/navigationsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\navigationsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tdjeuvssau", "Integrity": "odNLJcNym9pg8uDFic/EHxQ4pvuePXpWVuh3JQa8jAw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\navigationsbase.min.js", "FileLength": 3964, "LastWriteTime": "2025-07-31T14:12:34.2190201+00:00"}, "UAW0TKJhR0UgTy0eToOHNsvWSiNqSb+kxB6jC+M/sr8=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\nr2uivr86d-mmw5aypzb0.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-accordion.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-accordion.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xi476s1r1y", "Integrity": "V2e+/JiGf4fb0ioTvLpJ3+H50NChWorFYOLaygd99B8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-accordion.min.js", "FileLength": 3213, "LastWriteTime": "2025-07-31T14:12:34.2309886+00:00"}, "m6Hq8xjgqcCfKYZp03RI5cW1saQXZoZTfM387xTa+ME=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\0pcagc9na8-87p4l2g8lq.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-breadcrumb.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5avmxfz04", "Integrity": "Le9djNuAk9kAcy1hJbYDkoUpjxp48lyZ1F3TrPbnLDg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "FileLength": 1576, "LastWriteTime": "2025-07-31T14:12:34.2389667+00:00"}, "zDTcr65UBfNTJt2WpiPDaNxZL/Ryr/XD+63hMLd7MUo=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\9ispkuv7ij-neeiqevn6f.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-carousel.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-carousel.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d2ejby2fuh", "Integrity": "9WDXMEXmETVRBJl0uX/Bs1HEvvCl6Vx/u+CGl8QiEdA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-carousel.min.js", "FileLength": 1921, "LastWriteTime": "2025-07-31T14:12:34.2527091+00:00"}, "E3DHyoKkQTOxpd/aEfXym2Maxiurk9L2uhARPxCpe4w=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\tqhdg3t9bq-uqwhurys37.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-contextmenu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-contextmenu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "652pca9afk", "Integrity": "Gtsk7rpBOXY/T91Lfo//jGWJwsaTzFMSXkayF3z5mc0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-contextmenu.min.js", "FileLength": 4683, "LastWriteTime": "2025-07-31T14:12:34.2698854+00:00"}, "H5J/04GBypATPlpQcI0CpLdRRQCRZlhJiToe7i8D/hU=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\eejr38ihun-3yq1pja1s9.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-dropdowntree.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ypdujo5j57", "Integrity": "Cvz4NjqBVSvecNfBzkOWx3KcKIP54vaN4tSFweOJDEg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "FileLength": 5694, "LastWriteTime": "2025-07-31T14:12:34.2838479+00:00"}, "aEZ9hovU/aNgAjG/H8I9wMmbINM5VPpssJ00Xd6NECY=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\djau0kle4k-7u9umw6dst.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-menu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-menu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0fa5ccdnlo", "Integrity": "/I/HyINbnrlgHx0KLaDGVMMmqf7t4KwJbp2GX+T3SyQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-menu.min.js", "FileLength": 3885, "LastWriteTime": "2025-07-31T14:12:34.246946+00:00"}, "X00LpXejRuGgCI7+NMs36tyad1/j599vAsobHOKQFG0=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\mfqtlt087y-a20odbfi5s.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-pager.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-pager.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jjrl3gni2b", "Integrity": "UW6QYLlXfX7w5IONRLw/CQ2qpM8oeFBnYeuGLHpGNlQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-pager.min.js", "FileLength": 2390, "LastWriteTime": "2025-07-31T14:12:34.203141+00:00"}, "JV+5KlTJVqR1tCGcqVGPFhvamS5g6e8R/eqQv/oGM+E=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ukontf0to4-aulwboaixq.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-sidebar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-sidebar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pweo231pls", "Integrity": "hyCTJz97AMDNSYhDtddN/DP5eNRRkMhp1bFmYCaKgSY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-sidebar.min.js", "FileLength": 2748, "LastWriteTime": "2025-07-31T14:12:34.2180228+00:00"}, "VDcZR/h6Plu2RaaPxFzgAX2/jTbj9JhgvHNiofgHbPc=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\grozcvm4ej-3jpai5pxoj.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-stepper.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-stepper.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e9vxb3ws6i", "Integrity": "fgwf80QQNF74t94ouzAmCSu8d51wR4RG0XHLL1Haw1U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-stepper.min.js", "FileLength": 3359, "LastWriteTime": "2025-07-31T14:12:34.2250923+00:00"}, "d3+DyS4lsNf5p8C6IPtDzlmsUYDL4DNR8X+E1MyzfYU=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jhhm3fquz3-xf4d1dg01c.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-tab.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-tab.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e268zfx6ee", "Integrity": "SgAd2LpYQZKAptnwN2vg6BeU5df5r+ezD4HDEJr9GwY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-tab.min.js", "FileLength": 7680, "LastWriteTime": "2025-07-31T14:12:34.2519329+00:00"}, "Pzn7Gz7uO7g3WTvIOgBJA16m0U/yw98wD9Z6CTg8FBI=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2m61bm772c-rb6ei14cmq.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-toolbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-toolbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9750mq0mst", "Integrity": "q+ed+iEDLNjMLxOIE3yFCDYfkvQS1MU/MLj/yoWJXwo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-toolbar.min.js", "FileLength": 9302, "LastWriteTime": "2025-07-31T14:12:34.2639003+00:00"}, "oAHEcwmq8ws0j4ZBSadMW7t5HoTSe5y1v2FP4DYtJ/8=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\diku15o4fe-4yr0zuexvq.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-treeview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-treeview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jesogd3v9q", "Integrity": "NQZiTysXepP1oHYxdFf6HZoL3fUKe0g9XT2/Ej+VeWc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-treeview.min.js", "FileLength": 11924, "LastWriteTime": "2025-07-31T14:12:34.2814703+00:00"}, "Bnxpd01U3nYE9xDKRIVbmayxdZa+xhbTTfugvMLs+X4=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\pl2kkyous1-m5nz426d3u.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-calendar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-calendar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d3m3rhwkj", "Integrity": "eAY8usc3t2ei8kDiiOMa6baFyaSb8Oym2A1MeX1Y/hk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-calendar.min.js", "FileLength": 810, "LastWriteTime": "2025-07-31T14:12:34.2898309+00:00"}, "M8wU29yLhg6FbENXjDWInrZvZywYNtVksp/J4mCii0M=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\u3460sy28a-r9k4j69dlg.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-datepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-datepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nn3w3u2v68", "Integrity": "fSqv6ik8pTG5f7KwvRzQriZKG57W0YkaYHzfcJfEQhE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-datepicker.min.js", "FileLength": 8658, "LastWriteTime": "2025-07-31T14:12:34.2549243+00:00"}, "pBbYCWs+sCruBf47PRcMaiAORrL4yIQF1Wr4/p2BjgA=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\nq0zaazork-8d7q9eto80.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-daterangepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "maq2p7ggpx", "Integrity": "YtlggMNbk3DdsHdrV9Aiuv3S3Y/paxeBiITnQ77ZiME=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "FileLength": 3813, "LastWriteTime": "2025-07-31T14:12:34.203141+00:00"}, "VNw+ED+EmBfJnw7ncfSRx49FMVlrY1jdXjfHPFVPS1E=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\6efprx52i0-xskb1o7vx7.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-timepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-timepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1u9pr0j1p3", "Integrity": "CWl3tZE6HXkIZ5uyMPeGrjnoLW/q6idcGNca1aSITeQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-timepicker.min.js", "FileLength": 7454, "LastWriteTime": "2025-07-31T14:12:34.2218283+00:00"}, "wi76GTEL2nhq5tTXOZhhCxfkAaZ5Lr7Fd8S2yexcGAM=": {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\klmnspddx3-yo5q9ha208.gz", "SourceId": "Syncfusion.Blazor.Grid", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Grid", "RelativePath": "scripts/sf-grid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\staticwebassets\\scripts\\sf-grid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3nc1cxyw78", "Integrity": "P8YicYiZCrYOvTzXxAhhffxcnLvE0/6wGKS0PUwv09Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\staticwebassets\\scripts\\sf-grid.min.js", "FileLength": 63468, "LastWriteTime": "2025-07-31T14:12:34.2389667+00:00"}}, "CachedCopyCandidates": {}}