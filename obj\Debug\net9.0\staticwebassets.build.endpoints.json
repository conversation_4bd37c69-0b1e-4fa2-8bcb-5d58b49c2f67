{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "Clients.html", "AssetFile": "Clients.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000449034576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=\""}, {"Name": "ETag", "Value": "W/\"VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk="}]}, {"Route": "Clients.html", "AssetFile": "Clients.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13575"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk="}]}, {"Route": "Clients.html.gz", "AssetFile": "Clients.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk="}]}, {"Route": "Clients.pbnxt20j60.html", "AssetFile": "Clients.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000449034576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=\""}, {"Name": "ETag", "Value": "W/\"VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbnxt20j60"}, {"Name": "integrity", "Value": "sha256-VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk="}, {"Name": "label", "Value": "Clients.html"}]}, {"Route": "Clients.pbnxt20j60.html", "AssetFile": "Clients.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13575"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbnxt20j60"}, {"Name": "integrity", "Value": "sha256-VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk="}, {"Name": "label", "Value": "Clients.html"}]}, {"Route": "Clients.pbnxt20j60.html.gz", "AssetFile": "Clients.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbnxt20j60"}, {"Name": "integrity", "Value": "sha256-4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk="}, {"Name": "label", "Value": "Clients.html.gz"}]}, {"Route": "Dashboard.html", "AssetFile": "Dashboard.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000176149375"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5676"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=\""}, {"Name": "ETag", "Value": "W/\"LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw="}]}, {"Route": "Dashboard.html", "AssetFile": "Dashboard.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24921"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw="}]}, {"Route": "Dashboard.html.gz", "AssetFile": "Dashboard.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5676"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764="}]}, {"Route": "Dashboard.ot006j5kmx.html", "AssetFile": "Dashboard.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000176149375"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5676"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=\""}, {"Name": "ETag", "Value": "W/\"LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ot006j5kmx"}, {"Name": "integrity", "Value": "sha256-LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw="}, {"Name": "label", "Value": "Dashboard.html"}]}, {"Route": "Dashboard.ot006j5kmx.html", "AssetFile": "Dashboard.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24921"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ot006j5kmx"}, {"Name": "integrity", "Value": "sha256-LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw="}, {"Name": "label", "Value": "Dashboard.html"}]}, {"Route": "Dashboard.ot006j5kmx.html.gz", "AssetFile": "Dashboard.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5676"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ot006j5kmx"}, {"Name": "integrity", "Value": "sha256-KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764="}, {"Name": "label", "Value": "Dashboard.html.gz"}]}, {"Route": "Gestion de stock.html", "AssetFile": "Gestion de stock.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000386697602"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2585"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=\""}, {"Name": "ETag", "Value": "W/\"zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM="}]}, {"Route": "Gestion de stock.html", "AssetFile": "Gestion de stock.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11187"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM="}]}, {"Route": "Gestion de stock.html.gz", "AssetFile": "Gestion de stock.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2585"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE="}]}, {"Route": "Gestion de stock.wl6n5gvn3c.html", "AssetFile": "Gestion de stock.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000386697602"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2585"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=\""}, {"Name": "ETag", "Value": "W/\"zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl6n5gvn3c"}, {"Name": "integrity", "Value": "sha256-zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM="}, {"Name": "label", "Value": "Gestion de stock.html"}]}, {"Route": "Gestion de stock.wl6n5gvn3c.html", "AssetFile": "Gestion de stock.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11187"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl6n5gvn3c"}, {"Name": "integrity", "Value": "sha256-zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM="}, {"Name": "label", "Value": "Gestion de stock.html"}]}, {"Route": "Gestion de stock.wl6n5gvn3c.html.gz", "AssetFile": "Gestion de stock.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2585"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl6n5gvn3c"}, {"Name": "integrity", "Value": "sha256-ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE="}, {"Name": "label", "Value": "Gestion de stock.html.gz"}]}, {"Route": "Home.html", "AssetFile": "Home.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000197083169"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=\""}, {"Name": "ETag", "Value": "W/\"KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc="}]}, {"Route": "Home.html", "AssetFile": "Home.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22265"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc="}]}, {"Route": "Home.html.gz", "AssetFile": "Home.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk="}]}, {"Route": "Home.o0kmq4r1qc.html", "AssetFile": "Home.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000197083169"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=\""}, {"Name": "ETag", "Value": "W/\"KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o0kmq4r1qc"}, {"Name": "integrity", "Value": "sha256-KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc="}, {"Name": "label", "Value": "Home.html"}]}, {"Route": "Home.o0kmq4r1qc.html", "AssetFile": "Home.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22265"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o0kmq4r1qc"}, {"Name": "integrity", "Value": "sha256-KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc="}, {"Name": "label", "Value": "Home.html"}]}, {"Route": "Home.o0kmq4r1qc.html.gz", "AssetFile": "Home.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o0kmq4r1qc"}, {"Name": "integrity", "Value": "sha256-h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk="}, {"Name": "label", "Value": "Home.html.gz"}]}, {"Route": "Interventions.html", "AssetFile": "Interventions.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000609756098"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1639"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=\""}, {"Name": "ETag", "Value": "W/\"l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY="}]}, {"Route": "Interventions.html", "AssetFile": "Interventions.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5682"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY="}]}, {"Route": "Interventions.html.gz", "AssetFile": "Interventions.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1639"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs="}]}, {"Route": "Interventions.xqeb31wrq2.html", "AssetFile": "Interventions.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000609756098"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1639"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=\""}, {"Name": "ETag", "Value": "W/\"l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xqeb31wrq2"}, {"Name": "integrity", "Value": "sha256-l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY="}, {"Name": "label", "Value": "Interventions.html"}]}, {"Route": "Interventions.xqeb31wrq2.html", "AssetFile": "Interventions.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5682"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xqeb31wrq2"}, {"Name": "integrity", "Value": "sha256-l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY="}, {"Name": "label", "Value": "Interventions.html"}]}, {"Route": "Interventions.xqeb31wrq2.html.gz", "AssetFile": "Interventions.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1639"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xqeb31wrq2"}, {"Name": "integrity", "Value": "sha256-osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs="}, {"Name": "label", "Value": "Interventions.html.gz"}]}, {"Route": "Login.7xmio610hh.html", "AssetFile": "Login.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000636537237"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1570"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=\""}, {"Name": "ETag", "Value": "W/\"dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xmio610hh"}, {"Name": "integrity", "Value": "sha256-dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk="}, {"Name": "label", "Value": "Login.html"}]}, {"Route": "Login.7xmio610hh.html", "AssetFile": "Login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5210"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xmio610hh"}, {"Name": "integrity", "Value": "sha256-dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk="}, {"Name": "label", "Value": "Login.html"}]}, {"Route": "Login.7xmio610hh.html.gz", "AssetFile": "Login.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1570"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xmio610hh"}, {"Name": "integrity", "Value": "sha256-okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4="}, {"Name": "label", "Value": "Login.html.gz"}]}, {"Route": "Login.html", "AssetFile": "Login.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000636537237"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1570"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=\""}, {"Name": "ETag", "Value": "W/\"dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk="}]}, {"Route": "Login.html", "AssetFile": "Login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5210"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk="}]}, {"Route": "Login.html.gz", "AssetFile": "Login.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1570"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4="}]}, {"Route": "Mission.2k64m4xzz5.html", "AssetFile": "Mission.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301932367"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3311"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=\""}, {"Name": "ETag", "Value": "W/\"MjDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2k64m4xzz5"}, {"Name": "integrity", "Value": "sha256-<PERSON>jDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE="}, {"Name": "label", "Value": "Mission.html"}]}, {"Route": "Mission.2k64m4xzz5.html", "AssetFile": "Mission.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17909"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"MjDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2k64m4xzz5"}, {"Name": "integrity", "Value": "sha256-<PERSON>jDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE="}, {"Name": "label", "Value": "Mission.html"}]}, {"Route": "Mission.2k64m4xzz5.html.gz", "AssetFile": "Mission.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3311"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2k64m4xzz5"}, {"Name": "integrity", "Value": "sha256-EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw="}, {"Name": "label", "Value": "Mission.html.gz"}]}, {"Route": "Mission.html", "AssetFile": "Mission.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301932367"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3311"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=\""}, {"Name": "ETag", "Value": "W/\"MjDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-<PERSON>jDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE="}]}, {"Route": "Mission.html", "AssetFile": "Mission.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17909"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"MjDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-<PERSON>jDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE="}]}, {"Route": "Mission.html.gz", "AssetFile": "Mission.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3311"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw="}]}, {"Route": "MonCompte.html", "AssetFile": "MonCompte.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000427167877"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2340"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=\""}, {"Name": "ETag", "Value": "W/\"UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk="}]}, {"Route": "MonCompte.html", "AssetFile": "MonCompte.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8845"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk="}]}, {"Route": "MonCompte.html.gz", "AssetFile": "MonCompte.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2340"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI="}]}, {"Route": "MonCompte.vg9bitalgh.html", "AssetFile": "MonCompte.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000427167877"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2340"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=\""}, {"Name": "ETag", "Value": "W/\"UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vg9bitalgh"}, {"Name": "integrity", "Value": "sha256-UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk="}, {"Name": "label", "Value": "MonCompte.html"}]}, {"Route": "MonCompte.vg9bitalgh.html", "AssetFile": "MonCompte.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8845"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vg9bitalgh"}, {"Name": "integrity", "Value": "sha256-UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk="}, {"Name": "label", "Value": "MonCompte.html"}]}, {"Route": "MonCompte.vg9bitalgh.html.gz", "AssetFile": "MonCompte.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2340"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vg9bitalgh"}, {"Name": "integrity", "Value": "sha256-7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI="}, {"Name": "label", "Value": "MonCompte.html.gz"}]}, {"Route": "Rapport.html", "AssetFile": "Rapport.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000461041955"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=\""}, {"Name": "ETag", "Value": "W/\"c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo="}]}, {"Route": "Rapport.html", "AssetFile": "Rapport.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10441"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo="}]}, {"Route": "Rapport.html.gz", "AssetFile": "Rapport.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU="}]}, {"Route": "Rapport.pdyi0fsnlt.html", "AssetFile": "Rapport.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000461041955"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=\""}, {"Name": "ETag", "Value": "W/\"c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pdyi0fsnlt"}, {"Name": "integrity", "Value": "sha256-c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo="}, {"Name": "label", "Value": "Rapport.html"}]}, {"Route": "Rapport.pdyi0fsnlt.html", "AssetFile": "Rapport.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10441"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pdyi0fsnlt"}, {"Name": "integrity", "Value": "sha256-c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo="}, {"Name": "label", "Value": "Rapport.html"}]}, {"Route": "Rapport.pdyi0fsnlt.html.gz", "AssetFile": "Rapport.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pdyi0fsnlt"}, {"Name": "integrity", "Value": "sha256-Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU="}, {"Name": "label", "Value": "Rapport.html.gz"}]}, {"Route": "Register.html", "AssetFile": "Register.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000515729758"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1938"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=\""}, {"Name": "ETag", "Value": "W/\"ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo="}]}, {"Route": "Register.html", "AssetFile": "Register.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6961"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo="}]}, {"Route": "Register.html.gz", "AssetFile": "Register.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1938"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE="}]}, {"Route": "Register.m6a14prokw.html", "AssetFile": "Register.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000515729758"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1938"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=\""}, {"Name": "ETag", "Value": "W/\"ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m6a14prokw"}, {"Name": "integrity", "Value": "sha256-ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo="}, {"Name": "label", "Value": "Register.html"}]}, {"Route": "Register.m6a14prokw.html", "AssetFile": "Register.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6961"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m6a14prokw"}, {"Name": "integrity", "Value": "sha256-ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo="}, {"Name": "label", "Value": "Register.html"}]}, {"Route": "Register.m6a14prokw.html.gz", "AssetFile": "Register.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1938"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m6a14prokw"}, {"Name": "integrity", "Value": "sha256-x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE="}, {"Name": "label", "Value": "Register.html.gz"}]}, {"Route": "Sites.gs3mxpddy3.html", "AssetFile": "Sites.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000392927308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2544"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=\""}, {"Name": "ETag", "Value": "W/\"UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gs3mxpddy3"}, {"Name": "integrity", "Value": "sha256-UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU="}, {"Name": "label", "Value": "Sites.html"}]}, {"Route": "Sites.gs3mxpddy3.html", "AssetFile": "Sites.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8279"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gs3mxpddy3"}, {"Name": "integrity", "Value": "sha256-UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU="}, {"Name": "label", "Value": "Sites.html"}]}, {"Route": "Sites.gs3mxpddy3.html.gz", "AssetFile": "Sites.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2544"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gs3mxpddy3"}, {"Name": "integrity", "Value": "sha256-G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc="}, {"Name": "label", "Value": "Sites.html.gz"}]}, {"Route": "Sites.html", "AssetFile": "Sites.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000392927308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2544"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=\""}, {"Name": "ETag", "Value": "W/\"UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU="}]}, {"Route": "Sites.html", "AssetFile": "Sites.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8279"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU="}]}, {"Route": "Sites.html.gz", "AssetFile": "Sites.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2544"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc="}]}, {"Route": "Style.css", "AssetFile": "Style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002320185615"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=\""}, {"Name": "ETag", "Value": "W/\"IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE="}]}, {"Route": "Style.css", "AssetFile": "Style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE="}]}, {"Route": "Style.css.gz", "AssetFile": "Style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs="}]}, {"Route": "Style.kjm711ejyc.css", "AssetFile": "Style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002320185615"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=\""}, {"Name": "ETag", "Value": "W/\"IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjm711ejyc"}, {"Name": "integrity", "Value": "sha256-IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE="}, {"Name": "label", "Value": "Style.css"}]}, {"Route": "Style.kjm711ejyc.css", "AssetFile": "Style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjm711ejyc"}, {"Name": "integrity", "Value": "sha256-IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE="}, {"Name": "label", "Value": "Style.css"}]}, {"Route": "Style.kjm711ejyc.css.gz", "AssetFile": "Style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:57 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjm711ejyc"}, {"Name": "integrity", "Value": "sha256-T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs="}, {"Name": "label", "Value": "Style.css.gz"}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.0ir6ibxc9a.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2092"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0ir6ibxc9a"}, {"Name": "integrity", "Value": "sha256-uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001324503311"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VYRLnTV2bt78z6Mvm/iSjeHfsQlRJ6warRKTBRzJTVg=\""}, {"Name": "ETag", "Value": "W/\"uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2092"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VYRLnTV2bt78z6Mvm/iSjeHfsQlRJ6warRKTBRzJTVg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VYRLnTV2bt78z6Mvm/iSjeHfsQlRJ6warRKTBRzJTVg="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.d2wfjv2vus.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d2wfjv2vus"}, {"Name": "integrity", "Value": "sha256-6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000396510706"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2521"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"l9T25TTVRJDMsQ37KXGjrgqHFV/HSSsq9Qty1ZJIG1k=\""}, {"Name": "ETag", "Value": "W/\"6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2521"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"l9T25TTVRJDMsQ37KXGjrgqHFV/HSSsq9Qty1ZJIG1k=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l9T25TTVRJDMsQ37KXGjrgqHFV/HSSsq9Qty1ZJIG1k="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001233045623"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "810"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eAY8usc3t2ei8kDiiOMa6baFyaSb8Oym2A1MeX1Y/hk=\""}, {"Name": "ETag", "Value": "W/\"BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1627"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "810"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eAY8usc3t2ei8kDiiOMa6baFyaSb8Oym2A1MeX1Y/hk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eAY8usc3t2ei8kDiiOMa6baFyaSb8Oym2A1MeX1Y/hk="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.m5nz426d3u.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1627"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m5nz426d3u"}, {"Name": "integrity", "Value": "sha256-BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000115486777"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8658"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fSqv6ik8pTG5f7KwvRzQriZKG57W0YkaYHzfcJfEQhE=\""}, {"Name": "ETag", "Value": "W/\"n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "38953"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8658"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fSqv6ik8pTG5f7KwvRzQriZKG57W0YkaYHzfcJfEQhE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fSqv6ik8pTG5f7KwvRzQriZKG57W0YkaYHzfcJfEQhE="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.r9k4j69dlg.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "38953"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r9k4j69dlg"}, {"Name": "integrity", "Value": "sha256-n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.8d7q9eto80.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15668"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8d7q9eto80"}, {"Name": "integrity", "Value": "sha256-zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000262191924"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3813"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YtlggMNbk3DdsHdrV9Aiuv3S3Y/paxeBiITnQ77ZiME=\""}, {"Name": "ETag", "Value": "W/\"zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15668"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3813"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YtlggMNbk3DdsHdrV9Aiuv3S3Y/paxeBiITnQ77ZiME=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YtlggMNbk3DdsHdrV9Aiuv3S3Y/paxeBiITnQ77ZiME="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000134138162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7454"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CWl3tZE6HXkIZ5uyMPeGrjnoLW/q6idcGNca1aSITeQ=\""}, {"Name": "ETag", "Value": "W/\"75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7454"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CWl3tZE6HXkIZ5uyMPeGrjnoLW/q6idcGNca1aSITeQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CWl3tZE6HXkIZ5uyMPeGrjnoLW/q6idcGNca1aSITeQ="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.xskb1o7vx7.js", "AssetFile": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xskb1o7vx7"}, {"Name": "integrity", "Value": "sha256-75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.buyzacakm8.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16205"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "buyzacakm8"}, {"Name": "integrity", "Value": "sha256-3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000235626767"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4243"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BRHnOK8U7cDSZgPiaN1GF6MWPxMxONHqj2q8eGzakuM=\""}, {"Name": "ETag", "Value": "W/\"3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16205"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4243"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BRHnOK8U7cDSZgPiaN1GF6MWPxMxONHqj2q8eGzakuM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BRHnOK8U7cDSZgPiaN1GF6MWPxMxONHqj2q8eGzakuM="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000224769611"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4448"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DYW/5c69o8fCUolvnIH4ijloP1Qu6US5SijIu0Cbr84=\""}, {"Name": "ETag", "Value": "W/\"Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4448"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DYW/5c69o8fCUolvnIH4ijloP1Qu6US5SijIu0Cbr84=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DYW/5c69o8fCUolvnIH4ijloP1Qu6US5SijIu0Cbr84="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.mzeb2kh2gi.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mzeb2kh2gi"}, {"Name": "integrity", "Value": "sha256-Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.jkhlsawj7m.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9381"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jkhlsawj7m"}, {"Name": "integrity", "Value": "sha256-MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000271149675"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3687"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6PKsxj/uvLNsJih81tS2vn15rG4zLiEJcLp7rz6odEw=\""}, {"Name": "ETag", "Value": "W/\"MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9381"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3687"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6PKsxj/uvLNsJih81tS2vn15rG4zLiEJcLp7rz6odEw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6PKsxj/uvLNsJih81tS2vn15rG4zLiEJcLp7rz6odEw="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.1jog2vdvqp.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1jog2vdvqp"}, {"Name": "integrity", "Value": "sha256-LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000078265634"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12776"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZnKvdEdeBNgXF4UWgw0ZEusbJpvdvSguVZ882RlZWrU=\""}, {"Name": "ETag", "Value": "W/\"LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12776"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZnKvdEdeBNgXF4UWgw0ZEusbJpvdvSguVZ882RlZWrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZnKvdEdeBNgXF4UWgw0ZEusbJpvdvSguVZ882RlZWrU="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000009790675"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "102137"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yjn/H+D9FW90HM2T05OHIbPgyhlHB+D6hWUn3L9s9Ds=\""}, {"Name": "ETag", "Value": "W/\"U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "248119"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "102137"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yjn/H+D9FW90HM2T05OHIbPgyhlHB+D6hWUn3L9s9Ds=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yjn/H+D9FW90HM2T05OHIbPgyhlHB+D6hWUn3L9s9Ds="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.ri75jdaj3r.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "248119"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ri75jdaj3r"}, {"Name": "integrity", "Value": "sha256-U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.by0shsu0jg.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4627949"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "by0shsu0jg"}, {"Name": "integrity", "Value": "sha256-03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000001009386"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "990700"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0+fg4riJa17YLHW0axaWYAnGEwnJQG79HosDok95qtw=\""}, {"Name": "ETag", "Value": "W/\"03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4627949"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "990700"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0+fg4riJa17YLHW0axaWYAnGEwnJQG79HosDok95qtw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0+fg4riJa17YLHW0axaWYAnGEwnJQG79HosDok95qtw="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "AssetFile": "_content/Syncfusion.Blazor.Data/scripts/data.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000039960040"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25024"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"z3Fa5jflZwuv7LLQxMSoLo/kuxF59w0qjMIwJQwOn+Q=\""}, {"Name": "ETag", "Value": "W/\"BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "AssetFile": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91365"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Data/scripts/data.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25024"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"z3Fa5jflZwuv7LLQxMSoLo/kuxF59w0qjMIwJQwOn+Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3Fa5jflZwuv7LLQxMSoLo/kuxF59w0qjMIwJQwOn+Q="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.k2m5n6fmft.js", "AssetFile": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "91365"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k2m5n6fmft"}, {"Name": "integrity", "Value": "sha256-BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Data/scripts/data.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.67586omov2.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "43225"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "67586omov2"}, {"Name": "integrity", "Value": "sha256-rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000103498241"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9661"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HOZ8mWf74M0BAkiCAiIOqbO3gp6K+L6ye3QocXOX098=\""}, {"Name": "ETag", "Value": "W/\"rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43225"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9661"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HOZ8mWf74M0BAkiCAiIOqbO3gp6K+L6ye3QocXOX098=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HOZ8mWf74M0BAkiCAiIOqbO3gp6K+L6ye3QocXOX098="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.9j6p65atja.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5559"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j6p65<PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000524109015"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1907"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4ku+jnyI4WcPVS0hujY7O0pF/UOm/+jv9WtB1klpd2o=\""}, {"Name": "ETag", "Value": "W/\"qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5559"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1907"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4ku+jnyI4WcPVS0hujY7O0pF/UOm/+jv9WtB1klpd2o=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4ku+jnyI4WcPVS0hujY7O0pF/UOm/+jv9WtB1klpd2o="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.4hzxl1uzz6.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21517"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4hzxl1uzz6"}, {"Name": "integrity", "Value": "sha256-KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000172950536"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5781"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WSje0yskblEFm2pjNYaF8ed1MQ/A0DCScJkoSA585EE=\""}, {"Name": "ETag", "Value": "W/\"KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21517"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5781"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WSje0yskblEFm2pjNYaF8ed1MQ/A0DCScJkoSA585EE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WSje0yskblEFm2pjNYaF8ed1MQ/A0DCScJkoSA585EE="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000118063754"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8469"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CFiKuIrnBTD3hRTjmOoWnmPPoXUugxZwQe5vlsZGXgA=\""}, {"Name": "ETag", "Value": "W/\"XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8469"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CFiKuIrnBTD3hRTjmOoWnmPPoXUugxZwQe5vlsZGXgA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CFiKuIrnBTD3hRTjmOoWnmPPoXUugxZwQe5vlsZGXgA="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.wdnlhme2ar.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wdnlhme2ar"}, {"Name": "integrity", "Value": "sha256-XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000317561131"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3148"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rRdLa/O0a/f4QZ55ICih3BZ0etmwyr2s2gIrIpWGmtI=\""}, {"Name": "ETag", "Value": "W/\"TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3148"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rRdLa/O0a/f4QZ55ICih3BZ0etmwyr2s2gIrIpWGmtI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rRdLa/O0a/f4QZ55ICih3BZ0etmwyr2s2gIrIpWGmtI="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.ykoprjbkzz.js", "AssetFile": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ykoprjbkzz"}, {"Name": "integrity", "Value": "sha256-TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js", "AssetFile": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015755723"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P8YicYiZCrYOvTzXxAhhffxcnLvE0/6wGKS0PUwv09Q=\""}, {"Name": "ETag", "Value": "W/\"rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw="}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js", "AssetFile": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "314071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw="}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "63468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P8YicYiZCrYOvTzXxAhhffxcnLvE0/6wGKS0PUwv09Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P8YicYiZCrYOvTzXxAhhffxcnLvE0/6wGKS0PUwv09Q="}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.yo5q9ha208.js", "AssetFile": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "314071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yo5q9ha208"}, {"Name": "integrity", "Value": "sha256-rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.bdnhf5zdvw.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6110"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bdnhf5zdvw"}, {"Name": "integrity", "Value": "sha256-j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000516262261"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1936"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XBJZ5jbyuiWhgMIupg52r9BTMqCnMyRs253sr9Mndjs=\""}, {"Name": "ETag", "Value": "W/\"j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6110"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1936"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XBJZ5jbyuiWhgMIupg52r9BTMqCnMyRs253sr9Mndjs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XBJZ5jbyuiWhgMIupg52r9BTMqCnMyRs253sr9Mndjs="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000389559797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2566"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zhFDXLiucb0jw5ocM5WBfmYO56xBkyhOvrdLEI4gcaE=\""}, {"Name": "ETag", "Value": "W/\"Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2566"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zhFDXLiucb0jw5ocM5WBfmYO56xBkyhOvrdLEI4gcaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zhFDXLiucb0jw5ocM5WBfmYO56xBkyhOvrdLEI4gcaE="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.u8jmjqbpf2.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u8jmjqbpf2"}, {"Name": "integrity", "Value": "sha256-Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.fjcbrnxrtb.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13272"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fjcbrnxrtb"}, {"Name": "integrity", "Value": "sha256-O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000329272308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3036"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S2eq5BRVdDweA8Qh8cqQpTyaS3y0YxICEfj3YmsnEMM=\""}, {"Name": "ETag", "Value": "W/\"O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13272"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3036"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S2eq5BRVdDweA8Qh8cqQpTyaS3y0YxICEfj3YmsnEMM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2eq5BRVdDweA8Qh8cqQpTyaS3y0YxICEfj3YmsnEMM="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.cg29fk2gn2.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cg29fk2gn2"}, {"Name": "integrity", "Value": "sha256-VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001053740780"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "948"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"I4KnvqwCsqAis1iU0aCb0LOTcgdZY7RbtjRtdbiVvEs=\""}, {"Name": "ETag", "Value": "W/\"VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "948"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"I4KnvqwCsqAis1iU0aCb0LOTcgdZY7RbtjRtdbiVvEs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-I4KnvqwCsqAis1iU0aCb0LOTcgdZY7RbtjRtdbiVvEs="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000438212095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2281"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+dQJCfeGGJoL1/ZtpznF0DjWjmv9HNWHuZghME+P8yI=\""}, {"Name": "ETag", "Value": "W/\"bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2281"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+dQJCfeGGJoL1/ZtpznF0DjWjmv9HNWHuZghME+P8yI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+dQJCfeGGJoL1/ZtpznF0DjWjmv9HNWHuZghME+P8yI="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.vteibwr79c.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vteibwr79c"}, {"Name": "integrity", "Value": "sha256-bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000173852573"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5751"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BRJhIImstd4aM5hz+Qf0vpcz4ad7EIzblxrXY36nEXc=\""}, {"Name": "ETag", "Value": "W/\"IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23905"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5751"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BRJhIImstd4aM5hz+Qf0vpcz4ad7EIzblxrXY36nEXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BRJhIImstd4aM5hz+Qf0vpcz4ad7EIzblxrXY36nEXc="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.x0gcv3gb9d.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23905"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0gcv3gb9d"}, {"Name": "integrity", "Value": "sha256-IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000160616768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6225"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"98tM+/ygumT0DfanDWUi+Ev/kIr7s9BK8v2BEPJYS/Y=\""}, {"Name": "ETag", "Value": "W/\"pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31787"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6225"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"98tM+/ygumT0DfanDWUi+Ev/kIr7s9BK8v2BEPJYS/Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-98tM+/ygumT0DfanDWUi+Ev/kIr7s9BK8v2BEPJYS/Y="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.lsufkzl165.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31787"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lsufkzl165"}, {"Name": "integrity", "Value": "sha256-pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.g9zntctz7a.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g9zntctz7a"}, {"Name": "integrity", "Value": "sha256-RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001103752759"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "905"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gbVH8zeaCoboxjY1YJN1D0r3ZqCJY45dNiw9hm/RY7I=\""}, {"Name": "ETag", "Value": "W/\"RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "905"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gbVH8zeaCoboxjY1YJN1D0r3ZqCJY45dNiw9hm/RY7I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gbVH8zeaCoboxjY1YJN1D0r3ZqCJY45dNiw9hm/RY7I="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.j3z817ikhe.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j3z817ikhe"}, {"Name": "integrity", "Value": "sha256-r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001647446458"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "606"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P0Omx/MN0b5JoBSlcuJ4hegPFxyPLBgwwnauBQShyIU=\""}, {"Name": "ETag", "Value": "W/\"r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "606"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P0Omx/MN0b5JoBSlcuJ4hegPFxyPLBgwwnauBQShyIU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P0Omx/MN0b5JoBSlcuJ4hegPFxyPLBgwwnauBQShyIU="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000915750916"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1091"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PZn71QUo0qDByztlZYFVepsJWrX+ux3ZQDfQRf5QaHU=\""}, {"Name": "ETag", "Value": "W/\"B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2772"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1091"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PZn71QUo0qDByztlZYFVepsJWrX+ux3ZQDfQRf5QaHU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PZn71QUo0qDByztlZYFVepsJWrX+ux3ZQDfQRf5QaHU="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.x6kv5h1gyc.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2772"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x6kv5h1gyc"}, {"Name": "integrity", "Value": "sha256-B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.1zm61t9neq.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "83883"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1zm61t9neq"}, {"Name": "integrity", "Value": "sha256-zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000052512734"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19042"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4+L5ZLoTRFvukhCazVyEv9ffYYUHNkaCHJQGYUzThu8=\""}, {"Name": "ETag", "Value": "W/\"zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "83883"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "19042"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4+L5ZLoTRFvukhCazVyEv9ffYYUHNkaCHJQGYUzThu8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4+L5ZLoTRFvukhCazVyEv9ffYYUHNkaCHJQGYUzThu8="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "AssetFile": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000193348801"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yRtRpRHENr0dEYS5sUrDbfJeJ/yMAh+lhgkzRenIzSY=\""}, {"Name": "ETag", "Value": "W/\"+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "AssetFile": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yRtRpRHENr0dEYS5sUrDbfJeJ/yMAh+lhgkzRenIzSY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yRtRpRHENr0dEYS5sUrDbfJeJ/yMAh+lhgkzRenIzSY="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.m9eje2li2o.js", "AssetFile": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m9eje2li2o"}, {"Name": "integrity", "Value": "sha256-+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000252206810"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3964"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"odNLJcNym9pg8uDFic/EHxQ4pvuePXpWVuh3JQa8jAw=\""}, {"Name": "ETag", "Value": "W/\"LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19975"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3964"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"odNLJcNym9pg8uDFic/EHxQ4pvuePXpWVuh3JQa8jAw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-odNLJcNym9pg8uDFic/EHxQ4pvuePXpWVuh3JQa8jAw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.sm61wjcwzb.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19975"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sm61wjcwzb"}, {"Name": "integrity", "Value": "sha256-LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311138768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V2e+/JiGf4fb0ioTvLpJ3+H50NChWorFYOLaygd99B8=\""}, {"Name": "ETag", "Value": "W/\"kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12263"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V2e+/JiGf4fb0ioTvLpJ3+H50NChWorFYOLaygd99B8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V2e+/JiGf4fb0ioTvLpJ3+H50NChWorFYOLaygd99B8="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.mmw5aypzb0.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12263"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mmw5aypzb0"}, {"Name": "integrity", "Value": "sha256-kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.87p4l2g8lq.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4660"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87p4l2g8lq"}, {"Name": "integrity", "Value": "sha256-bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000634115409"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1576"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Le9djNuAk9kAcy1hJbYDkoUpjxp48lyZ1F3TrPbnLDg=\""}, {"Name": "ETag", "Value": "W/\"bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4660"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1576"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Le9djNuAk9kAcy1hJbYDkoUpjxp48lyZ1F3TrPbnLDg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Le9djNuAk9kAcy1hJbYDkoUpjxp48lyZ1F3TrPbnLDg="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000520291363"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1921"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9WDXMEXmETVRBJl0uX/Bs1HEvvCl6Vx/u+CGl8QiEdA=\""}, {"Name": "ETag", "Value": "W/\"keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1921"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9WDXMEXmETVRBJl0uX/Bs1HEvvCl6Vx/u+CGl8QiEdA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9WDXMEXmETVRBJl0uX/Bs1HEvvCl6Vx/u+CGl8QiEdA="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.neeiqevn6f.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "neeiqevn6f"}, {"Name": "integrity", "Value": "sha256-keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000213492741"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Gtsk7rpBOXY/T91Lfo//jGWJwsaTzFMSXkayF3z5mc0=\""}, {"Name": "ETag", "Value": "W/\"Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Gtsk7rpBOXY/T91Lfo//jGWJwsaTzFMSXkayF3z5mc0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Gtsk7rpBOXY/T91Lfo//jGWJwsaTzFMSXkayF3z5mc0="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.uqwhurys37.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uqwhurys37"}, {"Name": "integrity", "Value": "sha256-Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.3yq1pja1s9.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "30439"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3yq1pja1s9"}, {"Name": "integrity", "Value": "sha256-qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000175592625"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Cvz4NjqBVSvecNfBzkOWx3KcKIP54vaN4tSFweOJDEg=\""}, {"Name": "ETag", "Value": "W/\"qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30439"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Cvz4NjqBVSvecNfBzkOWx3KcKIP54vaN4tSFweOJDEg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cvz4NjqBVSvecNfBzkOWx3KcKIP54vaN4tSFweOJDEg="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.7u9umw6dst.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15332"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7u9umw6dst"}, {"Name": "integrity", "Value": "sha256-sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000257334020"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3885"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/I/HyINbnrlgHx0KLaDGVMMmqf7t4KwJbp2GX+T3SyQ=\""}, {"Name": "ETag", "Value": "W/\"sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15332"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3885"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/I/HyINbnrlgHx0KLaDGVMMmqf7t4KwJbp2GX+T3SyQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/I/HyINbnrlgHx0KLaDGVMMmqf7t4KwJbp2GX+T3SyQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.a20odbfi5s.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9943"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a20odbfi5s"}, {"Name": "integrity", "Value": "sha256-Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UW6QYLlXfX7w5IONRLw/CQ2qpM8oeFBnYeuGLHpGNlQ=\""}, {"Name": "ETag", "Value": "W/\"Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9943"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UW6QYLlXfX7w5IONRLw/CQ2qpM8oeFBnYeuGLHpGNlQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UW6QYLlXfX7w5IONRLw/CQ2qpM8oeFBnYeuGLHpGNlQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.aulwboaixq.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10456"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aulwboaixq"}, {"Name": "integrity", "Value": "sha256-kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000363768643"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2748"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hyCTJz97AMDNSYhDtddN/DP5eNRRkMhp1bFmYCaKgSY=\""}, {"Name": "ETag", "Value": "W/\"kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10456"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2748"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hyCTJz97AMDNSYhDtddN/DP5eNRRkMhp1bFmYCaKgSY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hyCTJz97AMDNSYhDtddN/DP5eNRRkMhp1bFmYCaKgSY="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.3jpai5pxoj.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14111"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3jpai5pxoj"}, {"Name": "integrity", "Value": "sha256-QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000297619048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3359"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fgwf80QQNF74t94ouzAmCSu8d51wR4RG0XHLL1Haw1U=\""}, {"Name": "ETag", "Value": "W/\"QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14111"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3359"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fgwf80QQNF74t94ouzAmCSu8d51wR4RG0XHLL1Haw1U=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fgwf80QQNF74t94ouzAmCSu8d51wR4RG0XHLL1Haw1U="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000130191381"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7680"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SgAd2LpYQZKAptnwN2vg6BeU5df5r+ezD4HDEJr9GwY=\""}, {"Name": "ETag", "Value": "W/\"vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "34125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7680"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SgAd2LpYQZKAptnwN2vg6BeU5df5r+ezD4HDEJr9GwY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SgAd2LpYQZKAptnwN2vg6BeU5df5r+ezD4HDEJr9GwY="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.xf4d1dg01c.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "34125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xf4d1dg01c"}, {"Name": "integrity", "Value": "sha256-vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000107492207"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9302"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"q+ed+iEDLNjMLxOIE3yFCDYfkvQS1MU/MLj/yoWJXwo=\""}, {"Name": "ETag", "Value": "W/\"ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "9302"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"q+ed+iEDLNjMLxOIE3yFCDYfkvQS1MU/MLj/yoWJXwo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q+ed+iEDLNjMLxOIE3yFCDYfkvQS1MU/MLj/yoWJXwo="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.rb6ei14cmq.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "41177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rb6ei14cmq"}, {"Name": "integrity", "Value": "sha256-ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.4yr0zuexvq.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "49873"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yr0zuexvq"}, {"Name": "integrity", "Value": "sha256-fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083857442"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11924"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NQZiTysXepP1oHYxdFf6HZoL3fUKe0g9XT2/Ej+VeWc=\""}, {"Name": "ETag", "Value": "W/\"fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49873"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11924"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NQZiTysXepP1oHYxdFf6HZoL3fUKe0g9XT2/Ej+VeWc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NQZiTysXepP1oHYxdFf6HZoL3fUKe0g9XT2/Ej+VeWc="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "AssetFile": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000451059991"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2216"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OsMVazHqMjAjwfwXwli8LGa6lwXPJCyNsd5gKONG088=\""}, {"Name": "ETag", "Value": "W/\"qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "AssetFile": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2216"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OsMVazHqMjAjwfwXwli8LGa6lwXPJCyNsd5gKONG088=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OsMVazHqMjAjwfwXwli8LGa6lwXPJCyNsd5gKONG088="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.viv3nqn9op.js", "AssetFile": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "viv3nqn9op"}, {"Name": "integrity", "Value": "sha256-qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000174094708"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5743"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9nV3uGup69tcE0TavB1jPfRIaUh5AIzerTrukcCqLfg=\""}, {"Name": "ETag", "Value": "W/\"OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24870"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5743"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9nV3uGup69tcE0TavB1jPfRIaUh5AIzerTrukcCqLfg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9nV3uGup69tcE0TavB1jPfRIaUh5AIzerTrukcCqLfg="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.xddooubi4o.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24870"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xddooubi4o"}, {"Name": "integrity", "Value": "sha256-OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.e2u6q3p1lh.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e2u6q3p1lh"}, {"Name": "integrity", "Value": "sha256-SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000136855070"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7306"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"663pRccky4jzFU7mEhDEruVWQwL74Cq7hAgO+jjHzsc=\""}, {"Name": "ETag", "Value": "W/\"SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7306"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"663pRccky4jzFU7mEhDEruVWQwL74Cq7hAgO+jjHzsc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-663pRccky4jzFU7mEhDEruVWQwL74Cq7hAgO+jjHzsc="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.jqs3n24tf7.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jqs3n24tf7"}, {"Name": "integrity", "Value": "sha256-O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002118644068"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "471"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bOLNw1bgUPqHPOUK4WRLzDzyHxgE6es4bGpjBwfjTw8=\""}, {"Name": "ETag", "Value": "W/\"O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "471"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bOLNw1bgUPqHPOUK4WRLzDzyHxgE6es4bGpjBwfjTw8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bOLNw1bgUPqHPOUK4WRLzDzyHxgE6es4bGpjBwfjTw8="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.4h59cheqig.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11380"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4h59cheqig"}, {"Name": "integrity", "Value": "sha256-CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000295420975"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3384"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Tv00ASBTMUGgKKFdiZ0djWPDf6VTnpz7xusIVckSIkc=\""}, {"Name": "ETag", "Value": "W/\"CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11380"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3384"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Tv00ASBTMUGgKKFdiZ0djWPDf6VTnpz7xusIVckSIkc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tv00ASBTMUGgKKFdiZ0djWPDf6VTnpz7xusIVckSIkc="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000398247710"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2510"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QU7kJBL8MSk41KRAYHPDylFkQgmBSTRJXxXvDo8VF/I=\""}, {"Name": "ETag", "Value": "W/\"QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2510"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QU7kJBL8MSk41KRAYHPDylFkQgmBSTRJXxXvDo8VF/I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QU7kJBL8MSk41KRAYHPDylFkQgmBSTRJXxXvDo8VF/I="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.y4pke33q7n.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y4pke33q7n"}, {"Name": "integrity", "Value": "sha256-QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.aphbptuiu1.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3706"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aphbptuiu1"}, {"Name": "integrity", "Value": "sha256-XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000691085003"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1446"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6UU37OjaY6/vrGU+s3xH0DO1ehrN+ZyLdS5we1JzE6g=\""}, {"Name": "ETag", "Value": "W/\"XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3706"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js.gz", "AssetFile": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1446"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6UU37OjaY6/vrGU+s3xH0DO1ehrN+ZyLdS5we1JzE6g=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6UU37OjaY6/vrGU+s3xH0DO1ehrN+ZyLdS5we1JzE6g="}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000012424830"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "80483"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=\""}, {"Name": "ETag", "Value": "W/\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:37:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "81853"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}]}, {"Route": "favicon.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "80483"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:37:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I="}]}, {"Route": "favicon.tgup6kq3m2.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000012424830"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "80483"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=\""}, {"Name": "ETag", "Value": "W/\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:37:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgup6kq3m2"}, {"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.tgup6kq3m2.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "81853"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgup6kq3m2"}, {"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.tgup6kq3m2.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "80483"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:37:02 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgup6kq3m2"}, {"Name": "integrity", "Value": "sha256-pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I="}, {"Name": "label", "Value": "favicon.ico.gz"}]}, {"Route": "images/ChatGPT Image 22 juil. 2025, 13_51_40.n6eglj16dh.png", "AssetFile": "images/ChatGPT Image 22 juil. 2025, 13_51_40.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1800368"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z5xlFb1NMh9wVMGXpz+bL1/WfFOSvLDQziJqQUOaTPA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n6eglj16dh"}, {"Name": "integrity", "Value": "sha256-z5xlFb1NMh9wVMGXpz+bL1/WfFOSvLDQziJqQUOaTPA="}, {"Name": "label", "Value": "images/ChatGPT Image 22 juil. 2025, 13_51_40.png"}]}, {"Route": "images/ChatGPT Image 22 juil. 2025, 13_51_40.png", "AssetFile": "images/ChatGPT Image 22 juil. 2025, 13_51_40.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1800368"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z5xlFb1NMh9wVMGXpz+bL1/WfFOSvLDQziJqQUOaTPA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z5xlFb1NMh9wVMGXpz+bL1/WfFOSvLDQziJqQUOaTPA="}]}, {"Route": "images/ChatGPT Image 23 juil. 2025, 12_11_51.kx17dmg14z.png", "AssetFile": "images/ChatGPT Image 23 juil. 2025, 12_11_51.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1604670"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eHUky4q4kO+lhyvvCanyBTSvezc+uIYnRSFwTifWXNk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kx17dmg14z"}, {"Name": "integrity", "Value": "sha256-eHUky4q4kO+lhyvvCanyBTSvezc+uIYnRSFwTifWXNk="}, {"Name": "label", "Value": "images/ChatGPT Image 23 juil. 2025, 12_11_51.png"}]}, {"Route": "images/ChatGPT Image 23 juil. 2025, 12_11_51.png", "AssetFile": "images/ChatGPT Image 23 juil. 2025, 12_11_51.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1604670"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eHUky4q4kO+lhyvvCanyBTSvezc+uIYnRSFwTifWXNk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eHUky4q4kO+lhyvvCanyBTSvezc+uIYnRSFwTifWXNk="}]}, {"Route": "images/Engineer.png", "AssetFile": "images/Engineer.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "688006"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zpe06c4e14cI6tozLMgbHe45TjLdgErciBtlTBOpaTY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zpe06c4e14cI6tozLMgbHe45TjLdgErciBtlTBOpaTY="}]}, {"Route": "images/Engineer.qhl0j27a0c.png", "AssetFile": "images/Engineer.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "688006"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zpe06c4e14cI6tozLMgbHe45TjLdgErciBtlTBOpaTY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qhl0j27a0c"}, {"Name": "integrity", "Value": "sha256-zpe06c4e14cI6tozLMgbHe45TjLdgErciBtlTBOpaTY="}, {"Name": "label", "Value": "images/Engineer.png"}]}, {"Route": "images/Profile.png", "AssetFile": "images/Profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "81853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}]}, {"Route": "images/Profile.tgup6kq3m2.png", "AssetFile": "images/Profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "81853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgup6kq3m2"}, {"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}, {"Name": "label", "Value": "images/Profile.png"}]}, {"Route": "images/arrow-303116_1280.png", "AssetFile": "images/arrow-303116_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "124362"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zTW0FbBAUvNRJ7rSWsljWJp3YEpZHnlwxZIONGLozak=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zTW0FbBAUvNRJ7rSWsljWJp3YEpZHnlwxZIONGLozak="}]}, {"Route": "images/arrow-303116_1280.pteby9jfp2.png", "AssetFile": "images/arrow-303116_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "124362"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zTW0FbBAUvNRJ7rSWsljWJp3YEpZHnlwxZIONGLozak=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pteby9jfp2"}, {"Name": "integrity", "Value": "sha256-zTW0FbBAUvNRJ7rSWsljWJp3YEpZHnlwxZIONGLozak="}, {"Name": "label", "Value": "images/arrow-303116_1280.png"}]}, {"Route": "images/brain-1710293_1280.lwkossosp8.png", "AssetFile": "images/brain-1710293_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "197178"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1Yvas6AJovw5WS+lKhl+8/axHtWbnmSKyZI6YpnqHUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lwkossosp8"}, {"Name": "integrity", "Value": "sha256-1Yvas6AJovw5WS+lKhl+8/axHtWbnmSKyZI6YpnqHUk="}, {"Name": "label", "Value": "images/brain-1710293_1280.png"}]}, {"Route": "images/brain-1710293_1280.png", "AssetFile": "images/brain-1710293_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "197178"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1Yvas6AJovw5WS+lKhl+8/axHtWbnmSKyZI6YpnqHUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1Yvas6AJovw5WS+lKhl+8/axHtWbnmSKyZI6YpnqHUk="}]}, {"Route": "images/computer-8070002_1280.jpg", "AssetFile": "images/computer-8070002_1280.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "395581"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"8fKglCKLBooRwr+kwcg3XbfBusiBzb6rpclvym9uZx4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8fKglCKLBooRwr+kwcg3XbfBusiBzb6rpclvym9uZx4="}]}, {"Route": "images/computer-8070002_1280.xcmnicqqpw.jpg", "AssetFile": "images/computer-8070002_1280.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "395581"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"8fKglCKLBooRwr+kwcg3XbfBusiBzb6rpclvym9uZx4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xcmnicqqpw"}, {"Name": "integrity", "Value": "sha256-8fKglCKLBooRwr+kwcg3XbfBusiBzb6rpclvym9uZx4="}, {"Name": "label", "Value": "images/computer-8070002_1280.jpg"}]}, {"Route": "images/information-275708_1280.4zd9fdnpjz.png", "AssetFile": "images/information-275708_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1442675"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"AN7w8KNMJxOT+Hn6kuo+8pqOhByhnEvPEhooXwSlOqE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4zd9fdnpjz"}, {"Name": "integrity", "Value": "sha256-AN7w8KNMJxOT+Hn6kuo+8pqOhByhnEvPEhooXwSlOqE="}, {"Name": "label", "Value": "images/information-275708_1280.png"}]}, {"Route": "images/information-275708_1280.png", "AssetFile": "images/information-275708_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1442675"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"AN7w8KNMJxOT+Hn6kuo+8pqOhByhnEvPEhooXwSlOqE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AN7w8KNMJxOT+Hn6kuo+8pqOhByhnEvPEhooXwSlOqE="}]}, {"Route": "images/investigation-9604083_1280-removebg-preview.d4djnk86q1.png", "AssetFile": "images/investigation-9604083_1280-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "119530"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"LRDM8biSAIMVifVe8b8ZB1sj/Idyw2MtKgoiQDJJ8kY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4djnk86q1"}, {"Name": "integrity", "Value": "sha256-LRDM8biSAIMVifVe8b8ZB1sj/Idyw2MtKgoiQDJJ8kY="}, {"Name": "label", "Value": "images/investigation-9604083_1280-removebg-preview.png"}]}, {"Route": "images/investigation-9604083_1280-removebg-preview.png", "AssetFile": "images/investigation-9604083_1280-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "119530"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"LRDM8biSAIMVifVe8b8ZB1sj/Idyw2MtKgoiQDJJ8kY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LRDM8biSAIMVifVe8b8ZB1sj/Idyw2MtKgoiQDJJ8kY="}]}, {"Route": "images/lightbulb-1344763_1280.dbjrebzixc.jpg", "AssetFile": "images/lightbulb-1344763_1280.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "59824"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"d9ufWX/mt0HqG3+bUtAJdqQTULOCzPyh+80J5PszE2s=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dbjrebzixc"}, {"Name": "integrity", "Value": "sha256-d9ufWX/mt0HqG3+bUtAJdqQTULOCzPyh+80J5PszE2s="}, {"Name": "label", "Value": "images/lightbulb-1344763_1280.jpg"}]}, {"Route": "images/lightbulb-1344763_1280.jpg", "AssetFile": "images/lightbulb-1344763_1280.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "59824"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"d9ufWX/mt0HqG3+bUtAJdqQTULOCzPyh+80J5PszE2s=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d9ufWX/mt0HqG3+bUtAJdqQTULOCzPyh+80J5PszE2s="}]}, {"Route": "images/man-8106958_1280-removebg-preview.9xuud5qfxm.png", "AssetFile": "images/man-8106958_1280-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "138762"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"DbleI21Wx+knF4v7+U6f4nT00//UBOrY92+h8Q2J7ig=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9xuud5qfxm"}, {"Name": "integrity", "Value": "sha256-DbleI21Wx+knF4v7+U6f4nT00//UBOrY92+h8Q2J7ig="}, {"Name": "label", "Value": "images/man-8106958_1280-removebg-preview.png"}]}, {"Route": "images/man-8106958_1280-removebg-preview.png", "AssetFile": "images/man-8106958_1280-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "138762"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"DbleI21Wx+knF4v7+U6f4nT00//UBOrY92+h8Q2J7ig=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DbleI21Wx+knF4v7+U6f4nT00//UBOrY92+h8Q2J7ig="}]}, {"Route": "images/man-8106958_1280.png", "AssetFile": "images/man-8106958_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "219120"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ZSmo6T2OOqDI7mfPOj/HEMcFGlFet9PfXZfLT4XHKNk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZSmo6T2OOqDI7mfPOj/HEMcFGlFet9PfXZfLT4XHKNk="}]}, {"Route": "images/man-8106958_1280.v39exk6n1y.png", "AssetFile": "images/man-8106958_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "219120"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ZSmo6T2OOqDI7mfPOj/HEMcFGlFet9PfXZfLT4XHKNk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v39exk6n1y"}, {"Name": "integrity", "Value": "sha256-ZSmo6T2OOqDI7mfPOj/HEMcFGlFet9PfXZfLT4XHKNk="}, {"Name": "label", "Value": "images/man-8106958_1280.png"}]}, {"Route": "images/man-9553723_1280.29ipq6tdix.png", "AssetFile": "images/man-9553723_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "252779"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"WpXvLbzqc9ipSXL+34vK51tD1KSU9mshn+0uVMgNwo8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "29ipq6tdix"}, {"Name": "integrity", "Value": "sha256-WpXvLbzqc9ipSXL+34vK51tD1KSU9mshn+0uVMgNwo8="}, {"Name": "label", "Value": "images/man-9553723_1280.png"}]}, {"Route": "images/man-9553723_1280.png", "AssetFile": "images/man-9553723_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "252779"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"WpXvLbzqc9ipSXL+34vK51tD1KSU9mshn+0uVMgNwo8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WpXvLbzqc9ipSXL+34vK51tD1KSU9mshn+0uVMgNwo8="}]}, {"Route": "images/profile-42914_1280.cf9t04yl97.png", "AssetFile": "images/profile-42914_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17787"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CDvQqAOxRqn/Pj3p1V2Wn8nHEUC1hyAJGFRAJ1tmS+E=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cf9t04yl97"}, {"Name": "integrity", "Value": "sha256-CDvQqAOxRqn/Pj3p1V2Wn8nHEUC1hyAJGFRAJ1tmS+E="}, {"Name": "label", "Value": "images/profile-42914_1280.png"}]}, {"Route": "images/profile-42914_1280.png", "AssetFile": "images/profile-42914_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17787"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CDvQqAOxRqn/Pj3p1V2Wn8nHEUC1hyAJGFRAJ1tmS+E=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CDvQqAOxRqn/Pj3p1V2Wn8nHEUC1hyAJGFRAJ1tmS+E="}]}, {"Route": "images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.gkgbygsbb1.png", "AssetFile": "images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "208301"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oEpb678wDT1h12J+yOnfWrskxY2nVKeebLzb8Jwak6w=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gkgbygsbb1"}, {"Name": "integrity", "Value": "sha256-oEpb678wDT1h12J+yOnfWrskxY2nVKeebLzb8Jwak6w="}, {"Name": "label", "Value": "images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png"}]}, {"Route": "images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png", "AssetFile": "images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "208301"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oEpb678wDT1h12J+yOnfWrskxY2nVKeebLzb8Jwak6w=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oEpb678wDT1h12J+yOnfWrskxY2nVKeebLzb8Jwak6w="}]}]}