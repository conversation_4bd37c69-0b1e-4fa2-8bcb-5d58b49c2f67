{"Version": 1, "Hash": "gqiHeKtI332QxeVrIcikNNPk1wdloD4dkxGqxt/r2CQ=", "Source": "VTLeociaWebApp", "BasePath": "_content/VTLeociaWebApp", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "VTLeociaWebApp\\wwwroot", "Source": "VTLeociaWebApp", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "Pattern": "**"}], "Assets": [{"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\0pcagc9na8-87p4l2g8lq.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-breadcrumb.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5avmxfz04", "Integrity": "Le9djNuAk9kAcy1hJbYDkoUpjxp48lyZ1F3TrPbnLDg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "FileLength": 1576, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\0yyctx1peb-9j6p65atja.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-listbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-listbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u1q8ipkat6", "Integrity": "4ku+jnyI4WcPVS0hujY7O0pF/UOm/+jv9WtB1klpd2o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-listbox.min.js", "FileLength": 1907, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\12khajnlde-cg29fk2gn2.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-otp-input.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-otp-input.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rykoqi2klb", "Integrity": "I4KnvqwCsqAis1iU0aCb0LOTcgdZY7RbtjRtdbiVvEs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-otp-input.min.js", "FileLength": 948, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\1nafdd6chw-tgup6kq3m2.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "favicon#[.{fingerprint=tgup6kq3m2}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7lue81jric", "Integrity": "pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\favicon.ico", "FileLength": 80483, "LastWriteTime": "2025-07-31T13:37:02+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2m61bm772c-rb6ei14cmq.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-toolbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-toolbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9750mq0mst", "Integrity": "q+ed+iEDLNjMLxOIE3yFCDYfkvQS1MU/MLj/yoWJXwo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-toolbar.min.js", "FileLength": 9302, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2ndbdiwf0k-jqs3n24tf7.gz", "SourceId": "Syncfusion.Blazor.Spinner", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Spinner", "RelativePath": "scripts/sf-spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\sf-spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sewm4nhubs", "Integrity": "bOLNw1bgUPqHPOUK4WRLzDzyHxgE6es4bGpjBwfjTw8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\sf-spinner.min.js", "FileLength": 471, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2pse2bi12y-vg9bitalgh.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "MonCompte#[.{fingerprint=vg9bitalgh}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\MonCompte.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ax83nn60c7", "Integrity": "7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\MonCompte.html", "FileLength": 2340, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\3spo2eianh-e2u6q3p1lh.gz", "SourceId": "Syncfusion.Blazor.Popups", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Popups", "RelativePath": "scripts/sf-tooltip.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-tooltip.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pbv6q1n0a2", "Integrity": "663pRccky4jzFU7mEhDEruVWQwL74Cq7hAgO+jjHzsc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-tooltip.min.js", "FileLength": 7306, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4l15mon31j-m9eje2li2o.gz", "SourceId": "Syncfusion.Blazor.Lists", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Lists", "RelativePath": "scripts/sf-listview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\staticwebassets\\scripts\\sf-listview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t7zi9sg0mf", "Integrity": "yRtRpRHENr0dEYS5sUrDbfJeJ/yMAh+lhgkzRenIzSY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\staticwebassets\\scripts\\sf-listview.min.js", "FileLength": 5171, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4nl7ma3ohr-kjm711ejyc.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Style#[.{fingerprint=kjm711ejyc}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3jyu6p499j", "Integrity": "T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Style.css", "FileLength": 430, "LastWriteTime": "2025-07-31T13:51:57+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5a6tfv8f1g-pdyi0fsnlt.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Rapport#[.{fingerprint=pdyi0fsnlt}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Rapport.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ewll9l7nnb", "Integrity": "Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Rapport.html", "FileLength": 2168, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5h81voagx1-by0shsu0jg.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/syncfusion-blazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xbwu5l4tcp", "Integrity": "0+fg4riJa17YLHW0axaWYAnGEwnJQG79HosDok95qtw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "FileLength": 990700, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\6efprx52i0-xskb1o7vx7.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-timepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-timepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1u9pr0j1p3", "Integrity": "CWl3tZE6HXkIZ5uyMPeGrjnoLW/q6idcGNca1aSITeQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-timepicker.min.js", "FileLength": 7454, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\6wjchtuxco-u8jmjqbpf2.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-maskedtextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6u98uofk2b", "Integrity": "zhFDXLiucb0jw5ocM5WBfmYO56xBkyhOvrdLEI4gcaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "FileLength": 2566, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\90k8s08yp3-7xmio610hh.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Login#[.{fingerprint=7xmio610hh}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Login.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2s7p1y7a10", "Integrity": "okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Login.html", "FileLength": 1570, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\9ispkuv7ij-neeiqevn6f.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-carousel.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-carousel.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d2ejby2fuh", "Integrity": "9WDXMEXmETVRBJl0uX/Bs1HEvvCl6Vx/u+CGl8QiEdA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-carousel.min.js", "FileLength": 1921, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\9zg8dqxfdk-viv3nqn9op.gz", "SourceId": "Syncfusion.Blazor.Notifications", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Notifications", "RelativePath": "scripts/sf-toast.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\staticwebassets\\scripts\\sf-toast.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2adsh06qhk", "Integrity": "OsMVazHqMjAjwfwXwli8LGa6lwXPJCyNsd5gKONG088=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\staticwebassets\\scripts\\sf-toast.min.js", "FileLength": 2216, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\anji1rpska-ykoprjbkzz.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sortable.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sortable.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zcw6j3ed9t", "Integrity": "rRdLa/O0a/f4QZ55ICih3BZ0etmwyr2s2gIrIpWGmtI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sortable.min.js", "FileLength": 3148, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\bm0n96jk7v-y4pke33q7n.gz", "SourceId": "Syncfusion.Blazor.SplitButtons", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.SplitButtons", "RelativePath": "scripts/sf-drop-down-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d1hgg1ijyp", "Integrity": "QU7kJBL8MSk41KRAYHPDylFkQgmBSTRJXxXvDo8VF/I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "FileLength": 2510, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\cfe1nezmlr-gs3mxpddy3.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Sites#[.{fingerprint=gs3mxpddy3}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Sites.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tmfbkvgug7", "Integrity": "G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Sites.html", "FileLength": 2544, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ckt86gem57-1jog2vdvqp.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/svgbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\svgbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilm4r305hc", "Integrity": "ZnKvdEdeBNgXF4UWgw0ZEusbJpvdvSguVZ882RlZWrU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\svgbase.min.js", "FileLength": 12776, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\d77l2qy8cy-buyzacakm8.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/popup.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popup.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7h1es5aypm", "Integrity": "BRHnOK8U7cDSZgPiaN1GF6MWPxMxONHqj2q8eGzakuM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popup.min.js", "FileLength": 4243, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\diku15o4fe-4yr0zuexvq.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-treeview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-treeview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jesogd3v9q", "Integrity": "NQZiTysXepP1oHYxdFf6HZoL3fUKe0g9XT2/Ej+VeWc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-treeview.min.js", "FileLength": 11924, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\djau0kle4k-7u9umw6dst.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-menu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-menu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0fa5ccdnlo", "Integrity": "/I/HyINbnrlgHx0KLaDGVMMmqf7t4KwJbp2GX+T3SyQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-menu.min.js", "FileLength": 3885, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dnwq9wk44u-ri75jdaj3r.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/syncfusion-blazor-base.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yyq1vnkanj", "Integrity": "yjn/H+D9FW90HM2T05OHIbPgyhlHB+D6hWUn3L9s9Ds=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "FileLength": 102137, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dtw28fmir4-wl6n5gvn3c.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Gestion de stock#[.{fingerprint=wl6n5gvn3c}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Gestion de stock.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q4sc7o4ypy", "Integrity": "ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Gestion de stock.html", "FileLength": 2585, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\eejr38ihun-3yq1pja1s9.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-dropdowntree.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ypdujo5j57", "Integrity": "Cvz4NjqBVSvecNfBzkOWx3KcKIP54vaN4tSFweOJDEg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "FileLength": 5694, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\eiq2m65tqw-4hzxl1uzz6.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-mention.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-mention.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "94kimzvjjt", "Integrity": "WSje0yskblEFm2pjNYaF8ed1MQ/A0DCScJkoSA585EE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-mention.min.js", "FileLength": 5781, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ffhxaeu1yb-j3z817ikhe.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-textarea.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textarea.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n6dae95oz5", "Integrity": "P0Omx/MN0b5JoBSlcuJ4hegPFxyPLBgwwnauBQShyIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textarea.min.js", "FileLength": 606, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\g591birpkc-4h59cheqig.gz", "SourceId": "Syncfusion.Blazor.Spinner", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Spinner", "RelativePath": "scripts/spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "queyjsmx0k", "Integrity": "Tv00ASBTMUGgKKFdiZ0djWPDf6VTnpz7xusIVckSIkc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\spinner.min.js", "FileLength": 3384, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gk0sdbowrt-1zm61t9neq.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-uploader.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-uploader.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1n2zprm7ma", "Integrity": "4+L5ZLoTRFvukhCazVyEv9ffYYUHNkaCHJQGYUzThu8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-uploader.min.js", "FileLength": 19042, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\grozcvm4ej-3jpai5pxoj.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-stepper.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-stepper.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e9vxb3ws6i", "Integrity": "fgwf80QQNF74t94ouzAmCSu8d51wR4RG0XHLL1Haw1U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-stepper.min.js", "FileLength": 3359, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gyys9ii1lu-ot006j5kmx.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Dashboard#[.{fingerprint=ot006j5kmx}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Dashboard.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vmeviq3rrr", "Integrity": "KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Dashboard.html", "FileLength": 5676, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ha3ck1f4wq-0ir6ibxc9a.gz", "SourceId": "Syncfusion.Blazor.Buttons", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Buttons", "RelativePath": "scripts/sf-floating-action-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcynizji7r", "Integrity": "VYRLnTV2bt78z6Mvm/iSjeHfsQlRJ6warRKTBRzJTVg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "FileLength": 754, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\idlbwfjf3e-jkhlsawj7m.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/sf-svg-export.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\sf-svg-export.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o4c6chspmg", "Integrity": "6PKsxj/uvLNsJih81tS2vn15rG4zLiEJcLp7rz6odEw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\sf-svg-export.min.js", "FileLength": 3687, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jazegy34qu-lsufkzl165.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-slider.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-slider.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1epy3zj0bg", "Integrity": "98tM+/ygumT0DfanDWUi+Ev/kIr7s9BK8v2BEPJYS/Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-slider.min.js", "FileLength": 6225, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jhhm3fquz3-xf4d1dg01c.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-tab.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-tab.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e268zfx6ee", "Integrity": "SgAd2LpYQZKAptnwN2vg6BeU5df5r+ezD4HDEJr9GwY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-tab.min.js", "FileLength": 7680, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ji856uyn4d-2k64m4xzz5.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Mission#[.{fingerprint=2k64m4xzz5}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Mission.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "th9ozykzb6", "Integrity": "EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Mission.html", "FileLength": 3311, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jsshet654m-bdnhf5zdvw.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-colorpicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-colorpicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w59p7vvrse", "Integrity": "XBJZ5jbyuiWhgMIupg52r9BTMqCnMyRs253sr9Mndjs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-colorpicker.min.js", "FileLength": 1936, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\juc69m01qj-wdnlhme2ar.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-multiselect.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-multiselect.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c77qxgggi1", "Integrity": "CFiKuIrnBTD3hRTjmOoWnmPPoXUugxZwQe5vlsZGXgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-multiselect.min.js", "FileLength": 8469, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2dj3icwxz-pbnxt20j60.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Clients#[.{fingerprint=pbnxt20j60}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Clients.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b5uynd6xpu", "Integrity": "4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Clients.html", "FileLength": 2226, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2lc9f2kwg-xddooubi4o.gz", "SourceId": "Syncfusion.Blazor.Popups", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Popups", "RelativePath": "scripts/sf-dialog.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-dialog.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ueqpgxf806", "Integrity": "9nV3uGup69tcE0TavB1jPfRIaUh5AIzerTrukcCqLfg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-dialog.min.js", "FileLength": 5743, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\kg2ram854o-mzeb2kh2gi.gz", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/popupsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popupsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nnejunttvj", "Integrity": "DYW/5c69o8fCUolvnIH4ijloP1Qu6US5SijIu0Cbr84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popupsbase.min.js", "FileLength": 4448, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\klmnspddx3-yo5q9ha208.gz", "SourceId": "Syncfusion.Blazor.Grid", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Grid", "RelativePath": "scripts/sf-grid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\staticwebassets\\scripts\\sf-grid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3nc1cxyw78", "Integrity": "P8YicYiZCrYOvTzXxAhhffxcnLvE0/6wGKS0PUwv09Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\staticwebassets\\scripts\\sf-grid.min.js", "FileLength": 63468, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\kn04c7n2x3-g9zntctz7a.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-speechtotext.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-speechtotext.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7e0quenm8l", "Integrity": "gbVH8zeaCoboxjY1YJN1D0r3ZqCJY45dNiw9hm/RY7I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-speechtotext.min.js", "FileLength": 905, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\lc6ok7c9tb-k2m5n6fmft.gz", "SourceId": "Syncfusion.Blazor.Data", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Data", "RelativePath": "scripts/data.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\staticwebassets\\scripts\\data.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lrloyer4sr", "Integrity": "z3Fa5jflZwuv7LLQxMSoLo/kuxF59w0qjMIwJQwOn+Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\staticwebassets\\scripts\\data.min.js", "FileLength": 25024, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\mfqtlt087y-a20odbfi5s.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-pager.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-pager.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jjrl3gni2b", "Integrity": "UW6QYLlXfX7w5IONRLw/CQ2qpM8oeFBnYeuGLHpGNlQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-pager.min.js", "FileLength": 2390, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\nq0zaazork-8d7q9eto80.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-daterangepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "maq2p7ggpx", "Integrity": "YtlggMNbk3DdsHdrV9Aiuv3S3Y/paxeBiITnQ77ZiME=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "FileLength": 3813, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\nr2uivr86d-mmw5aypzb0.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-accordion.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-accordion.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xi476s1r1y", "Integrity": "V2e+/JiGf4fb0ioTvLpJ3+H50NChWorFYOLaygd99B8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-accordion.min.js", "FileLength": 3213, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\od31b7jcnl-xqeb31wrq2.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Interventions#[.{fingerprint=xqeb31wrq2}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Interventions.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uxukq9t0b8", "Integrity": "osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Interventions.html", "FileLength": 1639, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\onzwzcnqot-d2wfjv2vus.gz", "SourceId": "Syncfusion.Blazor.Buttons", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Buttons", "RelativePath": "scripts/sf-speeddial.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-speeddial.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpt0x5qokx", "Integrity": "l9T25TTVRJDMsQ37KXGjrgqHFV/HSSsq9Qty1ZJIG1k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-speeddial.min.js", "FileLength": 2521, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\oz70jm6r81-m6a14prokw.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Register#[.{fingerprint=m6a14prokw}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Register.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b7od6h08qt", "Integrity": "x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Register.html", "FileLength": 1938, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\p2zgtah1ds-aphbptuiu1.gz", "SourceId": "Syncfusion.Blazor.SplitButtons", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.SplitButtons", "RelativePath": "scripts/splitbuttonsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b36h10ukux", "Integrity": "6UU37OjaY6/vrGU+s3xH0DO1ehrN+ZyLdS5we1JzE6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "FileLength": 1446, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\pl2kkyous1-m5nz426d3u.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-calendar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-calendar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d3m3rhwkj", "Integrity": "eAY8usc3t2ei8kDiiOMa6baFyaSb8Oym2A1MeX1Y/hk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-calendar.min.js", "FileLength": 810, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\pq6ds00kew-x6kv5h1gyc.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-textbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n77thzgy20", "Integrity": "PZn71QUo0qDByztlZYFVepsJWrX+ux3ZQDfQRf5QaHU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textbox.min.js", "FileLength": 1091, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sadf1bszs1-fjcbrnxrtb.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-numerictextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n9rdkaa0r5", "Integrity": "S2eq5BRVdDweA8Qh8cqQpTyaS3y0YxICEfj3YmsnEMM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "FileLength": 3036, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sb4o3odzm8-x0gcv3gb9d.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-signature.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-signature.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tqw1ix8j0f", "Integrity": "BRJhIImstd4aM5hz+Qf0vpcz4ad7EIzblxrXY36nEXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-signature.min.js", "FileLength": 5751, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sekkta200r-67586omov2.gz", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-dropdownlist.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wr3od8<PERSON>oy", "Integrity": "HOZ8mWf74M0BAkiCAiIOqbO3gp6K+L6ye3QocXOX098=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "FileLength": 9661, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\tqhdg3t9bq-uqwhurys37.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-contextmenu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-contextmenu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "652pca9afk", "Integrity": "Gtsk7rpBOXY/T91Lfo//jGWJwsaTzFMSXkayF3z5mc0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-contextmenu.min.js", "FileLength": 4683, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\u3460sy28a-r9k4j69dlg.gz", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-datepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-datepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nn3w3u2v68", "Integrity": "fSqv6ik8pTG5f7KwvRzQriZKG57W0YkaYHzfcJfEQhE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-datepicker.min.js", "FileLength": 8658, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ukontf0to4-aulwboaixq.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-sidebar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-sidebar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pweo231pls", "Integrity": "hyCTJz97AMDNSYhDtddN/DP5eNRRkMhp1bFmYCaKgSY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-sidebar.min.js", "FileLength": 2748, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\vbtp9tnch6-vteibwr79c.gz", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-rating.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-rating.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5156q33gia", "Integrity": "+dQJCfeGGJoL1/ZtpznF0DjWjmv9HNWHuZghME+P8yI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-rating.min.js", "FileLength": 2281, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\vihrpo2kci-sm61wjcwzb.gz", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/navigationsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\navigationsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tdjeuvssau", "Integrity": "odNLJcNym9pg8uDFic/EHxQ4pvuePXpWVuh3JQa8jAw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\navigationsbase.min.js", "FileLength": 3964, "LastWriteTime": "2025-07-31T14:12:34+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\yb1j5ju6dz-o0kmq4r1qc.gz", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Home#[.{fingerprint=o0kmq4r1qc}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Home.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t7qn3zznmz", "Integrity": "h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Home.html", "FileLength": 5073, "LastWriteTime": "2025-07-31T10:21:58+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Clients.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Clients#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pbnxt20j60", "Integrity": "VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Clients.html", "FileLength": 13575, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Dashboard.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Dashboard#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ot006j5kmx", "Integrity": "LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Dashboard.html", "FileLength": 24921, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\favicon.ico", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tgup6kq3m2", "Integrity": "g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 81853, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Gestion de stock.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Gestion de stock#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wl6n5gvn3c", "Integrity": "zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Gestion de stock.html", "FileLength": 11187, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Home.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Home#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o0kmq4r1qc", "Integrity": "KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Home.html", "FileLength": 22265, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\arrow-303116_1280.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/arrow-303116_1280#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pteby9jfp2", "Integrity": "zTW0FbBAUvNRJ7rSWsljWJp3YEpZHnlwxZIONGLozak=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\arrow-303116_1280.png", "FileLength": 124362, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\brain-1710293_1280.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/brain-1710293_1280#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lwkossosp8", "Integrity": "1Yvas6AJovw5WS+lKhl+8/axHtWbnmSKyZI6YpnqHUk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\brain-1710293_1280.png", "FileLength": 197178, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\ChatGPT Image 22 juil. 2025, 13_51_40.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/ChatGPT Image 22 juil. 2025, 13_51_40#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n6eglj16dh", "Integrity": "z5xlFb1NMh9wVMGXpz+bL1/WfFOSvLDQziJqQUOaTPA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\ChatGPT Image 22 juil. 2025, 13_51_40.png", "FileLength": 1800368, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\ChatGPT Image 23 juil. 2025, 12_11_51.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/ChatGPT Image 23 juil. 2025, 12_11_51#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kx17dmg14z", "Integrity": "eHUky4q4kO+lhyvvCanyBTSvezc+uIYnRSFwTifWXNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\ChatGPT Image 23 juil. 2025, 12_11_51.png", "FileLength": 1604670, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\computer-8070002_1280.jpg", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/computer-8070002_1280#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xcmnicqqpw", "Integrity": "8fKglCKLBooRwr+kwcg3XbfBusiBzb6rpclvym9uZx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\computer-8070002_1280.jpg", "FileLength": 395581, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\Engineer.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/Engineer#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qhl0j27a0c", "Integrity": "zpe06c4e14cI6tozLMgbHe45TjLdgErciBtlTBOpaTY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\Engineer.png", "FileLength": 688006, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\information-275708_1280.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/information-275708_1280#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4zd9fdnpjz", "Integrity": "AN7w8KNMJxOT+Hn6kuo+8pqOhByhnEvPEhooXwSlOqE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\information-275708_1280.png", "FileLength": 1442675, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\investigation-9604083_1280-removebg-preview.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/investigation-9604083_1280-removebg-preview#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d4djnk86q1", "Integrity": "LRDM8biSAIMVifVe8b8ZB1sj/Idyw2MtKgoiQDJJ8kY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\investigation-9604083_1280-removebg-preview.png", "FileLength": 119530, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\lightbulb-1344763_1280.jpg", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/lightbulb-1344763_1280#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dbjrebzixc", "Integrity": "d9ufWX/mt0HqG3+bUtAJdqQTULOCzPyh+80J5PszE2s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\lightbulb-1344763_1280.jpg", "FileLength": 59824, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\man-8106958_1280-removebg-preview.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/man-8106958_1280-removebg-preview#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9xuud5qfxm", "Integrity": "DbleI21Wx+knF4v7+U6f4nT00//UBOrY92+h8Q2J7ig=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\man-8106958_1280-removebg-preview.png", "FileLength": 138762, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\man-8106958_1280.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/man-8106958_1280#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v39exk6n1y", "Integrity": "ZSmo6T2OOqDI7mfPOj/HEMcFGlFet9PfXZfLT4XHKNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\man-8106958_1280.png", "FileLength": 219120, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\man-9553723_1280.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/man-9553723_1280#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "29ipq6tdix", "Integrity": "WpXvLbzqc9ipSXL+34vK51tD1KSU9mshn+0uVMgNwo8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\man-9553723_1280.png", "FileLength": 252779, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\profile-42914_1280.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/profile-42914_1280#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cf9t04yl97", "Integrity": "CDvQqAOxRqn/Pj3p1V2Wn8nHEUC1hyAJGFRAJ1tmS+E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\profile-42914_1280.png", "FileLength": 17787, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\Profile.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/Profile#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tgup6kq3m2", "Integrity": "g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\Profile.png", "FileLength": 81853, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gkgbygsbb1", "Integrity": "oEpb678wDT1h12J+yOnfWrskxY2nVKeebLzb8Jwak6w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png", "FileLength": 208301, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Interventions.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Interventions#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xqeb31wrq2", "Integrity": "l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Interventions.html", "FileLength": 5682, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Login.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Login#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7xmio610hh", "Integrity": "dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Login.html", "FileLength": 5210, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Mission.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Mission#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2k64m4xzz5", "Integrity": "MjDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Mission.html", "FileLength": 17909, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\MonCompte.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "MonCompte#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vg9bitalgh", "Integrity": "UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\MonCompte.html", "FileLength": 8845, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Rapport.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Rapport#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pdyi0fsnlt", "Integrity": "c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Rapport.html", "FileLength": 10441, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Register.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Register#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m6a14prokw", "Integrity": "ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Register.html", "FileLength": 6961, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Sites.html", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Sites#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gs3mxpddy3", "Integrity": "UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Sites.html", "FileLength": 8279, "LastWriteTime": "2025-07-31T10:16:26+00:00"}, {"Identity": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Style.css", "SourceId": "VTLeociaWebApp", "SourceType": "Discovered", "ContentRoot": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\", "BasePath": "_content/VTLeociaWebApp", "RelativePath": "Style#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kjm711ejyc", "Integrity": "IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Style.css", "FileLength": 834, "LastWriteTime": "2025-07-31T13:51:38+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "SourceId": "Syncfusion.Blazor.Buttons", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Buttons", "RelativePath": "scripts/sf-floating-action-button.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0ir6ibxc9a", "Integrity": "uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "FileLength": 2092, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-speeddial.min.js", "SourceId": "Syncfusion.Blazor.Buttons", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Buttons", "RelativePath": "scripts/sf-speeddial.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d2wfjv2vus", "Integrity": "6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-speeddial.min.js", "FileLength": 11765, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-calendar.min.js", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-calendar.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m5nz426d3u", "Integrity": "BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-calendar.min.js", "FileLength": 1627, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-datepicker.min.js", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-datepicker.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r9k4j69dlg", "Integrity": "n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-datepicker.min.js", "FileLength": 38953, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-daterangepicker.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8d7q9eto80", "Integrity": "zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "FileLength": 15668, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-timepicker.min.js", "SourceId": "Syncfusion.Blazor.Calendars", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Calendars", "RelativePath": "scripts/sf-timepicker.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xskb1o7vx7", "Integrity": "75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-timepicker.min.js", "FileLength": 32954, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popup.min.js", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/popup.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "buyzacakm8", "Integrity": "3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popup.min.js", "FileLength": 16205, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popupsbase.min.js", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/popupsbase.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mzeb2kh2gi", "Integrity": "Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popupsbase.min.js", "FileLength": 14236, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\sf-svg-export.min.js", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/sf-svg-export.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jkhlsawj7m", "Integrity": "MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\sf-svg-export.min.js", "FileLength": 9381, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\svgbase.min.js", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/svgbase.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1jog2vdvqp", "Integrity": "LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\svgbase.min.js", "FileLength": 53438, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/syncfusion-blazor-base.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ri75jdaj3r", "Integrity": "U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "FileLength": 248119, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "SourceId": "Syncfusion.Blazor.Core", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Core", "RelativePath": "scripts/syncfusion-blazor.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "by0shsu0jg", "Integrity": "03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "FileLength": 4627949, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\staticwebassets\\scripts\\data.min.js", "SourceId": "Syncfusion.Blazor.Data", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Data", "RelativePath": "scripts/data.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k2m5n6fmft", "Integrity": "BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\staticwebassets\\scripts\\data.min.js", "FileLength": 91365, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-dropdownlist.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "67586omov2", "Integrity": "rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "FileLength": 43225, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-listbox.min.js", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-listbox.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9j6p65<PERSON><PERSON>", "Integrity": "qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-listbox.min.js", "FileLength": 5559, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-mention.min.js", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-mention.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4hzxl1uzz6", "Integrity": "KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-mention.min.js", "FileLength": 21517, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-multiselect.min.js", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sf-multiselect.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wdnlhme2ar", "Integrity": "XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-multiselect.min.js", "FileLength": 37989, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sortable.min.js", "SourceId": "Syncfusion.Blazor.DropDowns", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.DropDowns", "RelativePath": "scripts/sortable.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ykoprjbkzz", "Integrity": "TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sortable.min.js", "FileLength": 10531, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\staticwebassets\\scripts\\sf-grid.min.js", "SourceId": "Syncfusion.Blazor.Grid", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Grid", "RelativePath": "scripts/sf-grid.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yo5q9ha208", "Integrity": "rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\staticwebassets\\scripts\\sf-grid.min.js", "FileLength": 314071, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-colorpicker.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-colorpicker.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bdnhf5zdvw", "Integrity": "j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-colorpicker.min.js", "FileLength": 6110, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-maskedtextbox.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u8jmjqbpf2", "Integrity": "Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "FileLength": 9819, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-numerictextbox.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fjcbrnxrtb", "Integrity": "O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "FileLength": 13272, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-otp-input.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-otp-input.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cg29fk2gn2", "Integrity": "VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-otp-input.min.js", "FileLength": 2633, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-rating.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-rating.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vteibwr79c", "Integrity": "bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-rating.min.js", "FileLength": 9276, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-signature.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-signature.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x0gcv3gb9d", "Integrity": "IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-signature.min.js", "FileLength": 23905, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-slider.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-slider.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lsufkzl165", "Integrity": "pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-slider.min.js", "FileLength": 31787, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-speechtotext.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-speechtotext.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "g9zntctz7a", "Integrity": "RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-speechtotext.min.js", "FileLength": 2957, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textarea.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-textarea.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j3z817ikhe", "Integrity": "r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textarea.min.js", "FileLength": 1202, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textbox.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-textbox.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x6kv5h1gyc", "Integrity": "B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textbox.min.js", "FileLength": 2772, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-uploader.min.js", "SourceId": "Syncfusion.Blazor.Inputs", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Inputs", "RelativePath": "scripts/sf-uploader.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1zm61t9neq", "Integrity": "zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-uploader.min.js", "FileLength": 83883, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\staticwebassets\\scripts\\sf-listview.min.js", "SourceId": "Syncfusion.Blazor.Lists", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Lists", "RelativePath": "scripts/sf-listview.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m9eje2li2o", "Integrity": "+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\staticwebassets\\scripts\\sf-listview.min.js", "FileLength": 24435, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\navigationsbase.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/navigationsbase.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sm61wjcwzb", "Integrity": "LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\navigationsbase.min.js", "FileLength": 19975, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-accordion.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-accordion.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mmw5aypzb0", "Integrity": "kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-accordion.min.js", "FileLength": 12263, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-breadcrumb.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "87p4l2g8lq", "Integrity": "bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "FileLength": 4660, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-carousel.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-carousel.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "neeiqevn6f", "Integrity": "keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-carousel.min.js", "FileLength": 6563, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-contextmenu.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-contextmenu.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "uqwhurys37", "Integrity": "Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-contextmenu.min.js", "FileLength": 18233, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-dropdowntree.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3yq1pja1s9", "Integrity": "qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "FileLength": 30439, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-menu.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-menu.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7u9umw6dst", "Integrity": "sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-menu.min.js", "FileLength": 15332, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-pager.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-pager.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "a20odbfi5s", "Integrity": "Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-pager.min.js", "FileLength": 9943, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-sidebar.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-sidebar.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aulwboaixq", "Integrity": "kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-sidebar.min.js", "FileLength": 10456, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-stepper.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-stepper.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3jpai5pxoj", "Integrity": "QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-stepper.min.js", "FileLength": 14111, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-tab.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-tab.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xf4d1dg01c", "Integrity": "vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-tab.min.js", "FileLength": 34125, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-toolbar.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-toolbar.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rb6ei14cmq", "Integrity": "ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-toolbar.min.js", "FileLength": 41177, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-treeview.min.js", "SourceId": "Syncfusion.Blazor.Navigations", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Navigations", "RelativePath": "scripts/sf-treeview.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4yr0zuexvq", "Integrity": "fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-treeview.min.js", "FileLength": 49873, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\staticwebassets\\scripts\\sf-toast.min.js", "SourceId": "Syncfusion.Blazor.Notifications", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Notifications", "RelativePath": "scripts/sf-toast.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "viv3nqn9op", "Integrity": "qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\staticwebassets\\scripts\\sf-toast.min.js", "FileLength": 7214, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-dialog.min.js", "SourceId": "Syncfusion.Blazor.Popups", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Popups", "RelativePath": "scripts/sf-dialog.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xddooubi4o", "Integrity": "OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-dialog.min.js", "FileLength": 24870, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-tooltip.min.js", "SourceId": "Syncfusion.Blazor.Popups", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Popups", "RelativePath": "scripts/sf-tooltip.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e2u6q3p1lh", "Integrity": "SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-tooltip.min.js", "FileLength": 32214, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\sf-spinner.min.js", "SourceId": "Syncfusion.Blazor.Spinner", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Spinner", "RelativePath": "scripts/sf-spinner.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jqs3n24tf7", "Integrity": "O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\sf-spinner.min.js", "FileLength": 889, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\spinner.min.js", "SourceId": "Syncfusion.Blazor.Spinner", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.Spinner", "RelativePath": "scripts/spinner.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4h59cheqig", "Integrity": "CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\spinner.min.js", "FileLength": 11380, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "SourceId": "Syncfusion.Blazor.SplitButtons", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.SplitButtons", "RelativePath": "scripts/sf-drop-down-button.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "y4pke33q7n", "Integrity": "QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "FileLength": 9247, "LastWriteTime": "2025-07-28T09:45:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "SourceId": "Syncfusion.Blazor.SplitButtons", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\", "BasePath": "_content/Syncfusion.Blazor.SplitButtons", "RelativePath": "scripts/splitbuttonsbase.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aphbptuiu1", "Integrity": "XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "FileLength": 3706, "LastWriteTime": "2025-07-28T09:45:50+00:00"}], "Endpoints": [{"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.0ir6ibxc9a.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2092"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0ir6ibxc9a"}, {"Name": "integrity", "Value": "sha256-uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ha3ck1f4wq-0ir6ibxc9a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001324503311"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "ETag", "Value": "\"VYRLnTV2bt78z6Mvm/iSjeHfsQlRJ6warRKTBRzJTVg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2092"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uD403BKawZ1nh7/bKeXC1O+e2l/zSF3LB1rjPKKuAts="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-floating-action-button.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ha3ck1f4wq-0ir6ibxc9a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "754"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VYRLnTV2bt78z6Mvm/iSjeHfsQlRJ6warRKTBRzJTVg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VYRLnTV2bt78z6Mvm/iSjeHfsQlRJ6warRKTBRzJTVg="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.d2wfjv2vus.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-speeddial.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d2wfjv2vus"}, {"Name": "integrity", "Value": "sha256-6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\onzwzcnqot-d2wfjv2vus.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000396510706"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2521"}, {"Name": "ETag", "Value": "\"l9T25TTVRJDMsQ37KXGjrgqHFV/HSSsq9Qty1ZJIG1k=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\staticwebassets\\scripts\\sf-speeddial.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6wBOh14S8dbF069zvFlnXXGD41bjqkJXqAmObS8S1QY="}]}, {"Route": "_content/Syncfusion.Blazor.Buttons/scripts/sf-speeddial.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\onzwzcnqot-d2wfjv2vus.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2521"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"l9T25TTVRJDMsQ37KXGjrgqHFV/HSSsq9Qty1ZJIG1k=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l9T25TTVRJDMsQ37KXGjrgqHFV/HSSsq9Qty1ZJIG1k="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\pl2kkyous1-m5nz426d3u.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001233045623"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "810"}, {"Name": "ETag", "Value": "\"eAY8usc3t2ei8kDiiOMa6baFyaSb8Oym2A1MeX1Y/hk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-calendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1627"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\pl2kkyous1-m5nz426d3u.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "810"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eAY8usc3t2ei8kDiiOMa6baFyaSb8Oym2A1MeX1Y/hk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eAY8usc3t2ei8kDiiOMa6baFyaSb8Oym2A1MeX1Y/hk="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.m5nz426d3u.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-calendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1627"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m5nz426d3u"}, {"Name": "integrity", "Value": "sha256-BjSsbNd67C+t1bdWMc/yECbM6IRAn5Gt38c44e63DkU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-calendar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\u3460sy28a-r9k4j69dlg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000115486777"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8658"}, {"Name": "ETag", "Value": "\"fSqv6ik8pTG5f7KwvRzQriZKG57W0YkaYHzfcJfEQhE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "38953"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\u3460sy28a-r9k4j69dlg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8658"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fSqv6ik8pTG5f7KwvRzQriZKG57W0YkaYHzfcJfEQhE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fSqv6ik8pTG5f7KwvRzQriZKG57W0YkaYHzfcJfEQhE="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.r9k4j69dlg.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "38953"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r9k4j69dlg"}, {"Name": "integrity", "Value": "sha256-n9o/d2V2VRR90Frw+T/S81ITzfKTY0ofKmi7+t9DAKI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-datepicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.8d7q9eto80.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15668"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8d7q9eto80"}, {"Name": "integrity", "Value": "sha256-zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\nq0zaazork-8d7q9eto80.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000262191924"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3813"}, {"Name": "ETag", "Value": "\"YtlggMNbk3DdsHdrV9Aiuv3S3Y/paxeBiITnQ77ZiME=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15668"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zBgxrsgqA9dy6kN2/Dzl5Mg60X259rTKo341pFY695s="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-daterangepicker.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\nq0zaazork-8d7q9eto80.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3813"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YtlggMNbk3DdsHdrV9Aiuv3S3Y/paxeBiITnQ77ZiME=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YtlggMNbk3DdsHdrV9Aiuv3S3Y/paxeBiITnQ77ZiME="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\6efprx52i0-xskb1o7vx7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000134138162"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7454"}, {"Name": "ETag", "Value": "\"CWl3tZE6HXkIZ5uyMPeGrjnoLW/q6idcGNca1aSITeQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-timepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\6efprx52i0-xskb1o7vx7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7454"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CWl3tZE6HXkIZ5uyMPeGrjnoLW/q6idcGNca1aSITeQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CWl3tZE6HXkIZ5uyMPeGrjnoLW/q6idcGNca1aSITeQ="}]}, {"Route": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.xskb1o7vx7.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\staticwebassets\\scripts\\sf-timepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xskb1o7vx7"}, {"Name": "integrity", "Value": "sha256-75But8wXr3KTWm7r97LcF6xbtuSNeQp6s6BuORulNcI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Calendars/scripts/sf-timepicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.buyzacakm8.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popup.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "16205"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "buyzacakm8"}, {"Name": "integrity", "Value": "sha256-3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\d77l2qy8cy-buyzacakm8.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000235626767"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4243"}, {"Name": "ETag", "Value": "\"BRHnOK8U7cDSZgPiaN1GF6MWPxMxONHqj2q8eGzakuM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popup.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "16205"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3cqsK8QDzF7BBzGd9eJuQ316lZrTkFqBRrhbDKDVNcI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popup.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\d77l2qy8cy-buyzacakm8.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4243"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BRHnOK8U7cDSZgPiaN1GF6MWPxMxONHqj2q8eGzakuM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BRHnOK8U7cDSZgPiaN1GF6MWPxMxONHqj2q8eGzakuM="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\kg2ram854o-mzeb2kh2gi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000224769611"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4448"}, {"Name": "ETag", "Value": "\"DYW/5c69o8fCUolvnIH4ijloP1Qu6US5SijIu0Cbr84=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popupsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\kg2ram854o-mzeb2kh2gi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4448"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"DYW/5c69o8fCUolvnIH4ijloP1Qu6US5SijIu0Cbr84=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DYW/5c69o8fCUolvnIH4ijloP1Qu6US5SijIu0Cbr84="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.mzeb2kh2gi.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\popupsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14236"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mzeb2kh2gi"}, {"Name": "integrity", "Value": "sha256-Xj279DPFCbWqErLJrJK+oFqyySYCaRzyqjZxOmXrLSo="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/popupsbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.jkhlsawj7m.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\sf-svg-export.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9381"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jkhlsawj7m"}, {"Name": "integrity", "Value": "sha256-MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\idlbwfjf3e-jkhlsawj7m.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000271149675"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3687"}, {"Name": "ETag", "Value": "\"6PKsxj/uvLNsJih81tS2vn15rG4zLiEJcLp7rz6odEw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\sf-svg-export.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9381"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MxD3a7wrlRp9PvhIc7SLaa5pjjVcskivdWeU7IAUGkg="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/sf-svg-export.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\idlbwfjf3e-jkhlsawj7m.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3687"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6PKsxj/uvLNsJih81tS2vn15rG4zLiEJcLp7rz6odEw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6PKsxj/uvLNsJih81tS2vn15rG4zLiEJcLp7rz6odEw="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.1jog2vdvqp.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\svgbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1jog2vdvqp"}, {"Name": "integrity", "Value": "sha256-LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ckt86gem57-1jog2vdvqp.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000078265634"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12776"}, {"Name": "ETag", "Value": "\"ZnKvdEdeBNgXF4UWgw0ZEusbJpvdvSguVZ882RlZWrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\svgbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LY4EbrSX7+EP2Ez6pjEL2WVzdfzE6vI1wUVrl2YZ2jI="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/svgbase.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ckt86gem57-1jog2vdvqp.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12776"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZnKvdEdeBNgXF4UWgw0ZEusbJpvdvSguVZ882RlZWrU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZnKvdEdeBNgXF4UWgw0ZEusbJpvdvSguVZ882RlZWrU="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dnwq9wk44u-ri75jdaj3r.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000009790675"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "102137"}, {"Name": "ETag", "Value": "\"yjn/H+D9FW90HM2T05OHIbPgyhlHB+D6hWUn3L9s9Ds=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "248119"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dnwq9wk44u-ri75jdaj3r.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "102137"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yjn/H+D9FW90HM2T05OHIbPgyhlHB+D6hWUn3L9s9Ds=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yjn/H+D9FW90HM2T05OHIbPgyhlHB+D6hWUn3L9s9Ds="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.ri75jdaj3r.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "248119"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ri75jdaj3r"}, {"Name": "integrity", "Value": "sha256-U1xZmS0k7zYf4O6v2hl4jhuVrdNtwsSx8ET1Gza/KGo="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor-base.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.by0shsu0jg.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4627949"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "by0shsu0jg"}, {"Name": "integrity", "Value": "sha256-03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5h81voagx1-by0shsu0jg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000001009386"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "990700"}, {"Name": "ETag", "Value": "\"0+fg4riJa17YLHW0axaWYAnGEwnJQG79HosDok95qtw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4627949"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-03QYFrYISQ5pEFBo4INyuytSOPEMjVR8qJvEZmAK5Wc="}]}, {"Route": "_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5h81voagx1-by0shsu0jg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "990700"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0+fg4riJa17YLHW0axaWYAnGEwnJQG79HosDok95qtw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0+fg4riJa17YLHW0axaWYAnGEwnJQG79HosDok95qtw="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\lc6ok7c9tb-k2m5n6fmft.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000039960040"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25024"}, {"Name": "ETag", "Value": "\"z3Fa5jflZwuv7LLQxMSoLo/kuxF59w0qjMIwJQwOn+Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\staticwebassets\\scripts\\data.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "91365"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\lc6ok7c9tb-k2m5n6fmft.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25024"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"z3Fa5jflZwuv7LLQxMSoLo/kuxF59w0qjMIwJQwOn+Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3Fa5jflZwuv7LLQxMSoLo/kuxF59w0qjMIwJQwOn+Q="}]}, {"Route": "_content/Syncfusion.Blazor.Data/scripts/data.min.k2m5n6fmft.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\staticwebassets\\scripts\\data.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "91365"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k2m5n6fmft"}, {"Name": "integrity", "Value": "sha256-BELAEY8AtjrSDA+roWsMJXYj43Cd6imSS2HB0y8NiDA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Data/scripts/data.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.67586omov2.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "43225"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "67586omov2"}, {"Name": "integrity", "Value": "sha256-rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sekkta200r-67586omov2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000103498241"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9661"}, {"Name": "ETag", "Value": "\"HOZ8mWf74M0BAkiCAiIOqbO3gp6K+L6ye3QocXOX098=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "43225"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rq2oc7Uz7fz2Tk/+2cgAdZpgB6i8tRUX8TqTNnHvpvw="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-dropdownlist.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sekkta200r-67586omov2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9661"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HOZ8mWf74M0BAkiCAiIOqbO3gp6K+L6ye3QocXOX098=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HOZ8mWf74M0BAkiCAiIOqbO3gp6K+L6ye3QocXOX098="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.9j6p65atja.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-listbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5559"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9j6p65<PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\0yyctx1peb-9j6p65atja.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000524109015"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1907"}, {"Name": "ETag", "Value": "\"4ku+jnyI4WcPVS0hujY7O0pF/UOm/+jv9WtB1klpd2o=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-listbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5559"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qzjghORxh5yRPCD3r2F/osy1q5rQdTK+ZgXYzmcifpA="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-listbox.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\0yyctx1peb-9j6p65atja.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1907"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4ku+jnyI4WcPVS0hujY7O0pF/UOm/+jv9WtB1klpd2o=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4ku+jnyI4WcPVS0hujY7O0pF/UOm/+jv9WtB1klpd2o="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.4hzxl1uzz6.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-mention.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21517"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4hzxl1uzz6"}, {"Name": "integrity", "Value": "sha256-KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\eiq2m65tqw-4hzxl1uzz6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000172950536"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5781"}, {"Name": "ETag", "Value": "\"WSje0yskblEFm2pjNYaF8ed1MQ/A0DCScJkoSA585EE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-mention.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21517"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KFXhXqiX1FGunjRvhxco1QdRagAj/gSfHoX5tBB+FFk="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-mention.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\eiq2m65tqw-4hzxl1uzz6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5781"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WSje0yskblEFm2pjNYaF8ed1MQ/A0DCScJkoSA585EE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WSje0yskblEFm2pjNYaF8ed1MQ/A0DCScJkoSA585EE="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\juc69m01qj-wdnlhme2ar.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000118063754"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8469"}, {"Name": "ETag", "Value": "\"CFiKuIrnBTD3hRTjmOoWnmPPoXUugxZwQe5vlsZGXgA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-multiselect.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\juc69m01qj-wdnlhme2ar.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8469"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CFiKuIrnBTD3hRTjmOoWnmPPoXUugxZwQe5vlsZGXgA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CFiKuIrnBTD3hRTjmOoWnmPPoXUugxZwQe5vlsZGXgA="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.wdnlhme2ar.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sf-multiselect.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37989"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wdnlhme2ar"}, {"Name": "integrity", "Value": "sha256-XG2tKOHttB3wqioE4AWmkfEhzP8OxD/y6wNJTNW/26c="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sf-multiselect.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\anji1rpska-ykoprjbkzz.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000317561131"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3148"}, {"Name": "ETag", "Value": "\"rRdLa/O0a/f4QZ55ICih3BZ0etmwyr2s2gIrIpWGmtI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sortable.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\anji1rpska-ykoprjbkzz.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3148"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rRdLa/O0a/f4QZ55ICih3BZ0etmwyr2s2gIrIpWGmtI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rRdLa/O0a/f4QZ55ICih3BZ0etmwyr2s2gIrIpWGmtI="}]}, {"Route": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.ykoprjbkzz.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\staticwebassets\\scripts\\sortable.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10531"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ykoprjbkzz"}, {"Name": "integrity", "Value": "sha256-TuDt7EuWwz+VwlgzbhK9n0B19N6lx0HldUIfRntC/o4="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.DropDowns/scripts/sortable.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\klmnspddx3-yo5q9ha208.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015755723"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63468"}, {"Name": "ETag", "Value": "\"P8YicYiZCrYOvTzXxAhhffxcnLvE0/6wGKS0PUwv09Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw="}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\staticwebassets\\scripts\\sf-grid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "314071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw="}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\klmnspddx3-yo5q9ha208.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63468"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P8YicYiZCrYOvTzXxAhhffxcnLvE0/6wGKS0PUwv09Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P8YicYiZCrYOvTzXxAhhffxcnLvE0/6wGKS0PUwv09Q="}]}, {"Route": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.yo5q9ha208.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\staticwebassets\\scripts\\sf-grid.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "314071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yo5q9ha208"}, {"Name": "integrity", "Value": "sha256-rrNOWEkqRd2Zr1ISBm1by8za1C4EyhqcHXw0vZF9xiw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Grid/scripts/sf-grid.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.bdnhf5zdvw.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-colorpicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6110"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bdnhf5zdvw"}, {"Name": "integrity", "Value": "sha256-j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jsshet654m-bdnhf5zdvw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000516262261"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1936"}, {"Name": "ETag", "Value": "\"XBJZ5jbyuiWhgMIupg52r9BTMqCnMyRs253sr9Mndjs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-colorpicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6110"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j6PJcb7N8Pg1uk6vUKXEXQffpe0m/zbAjsVSCRtmd1M="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-colorpicker.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jsshet654m-bdnhf5zdvw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1936"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XBJZ5jbyuiWhgMIupg52r9BTMqCnMyRs253sr9Mndjs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XBJZ5jbyuiWhgMIupg52r9BTMqCnMyRs253sr9Mndjs="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\6wjchtuxco-u8jmjqbpf2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000389559797"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2566"}, {"Name": "ETag", "Value": "\"zhFDXLiucb0jw5ocM5WBfmYO56xBkyhOvrdLEI4gcaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\6wjchtuxco-u8jmjqbpf2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2566"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zhFDXLiucb0jw5ocM5WBfmYO56xBkyhOvrdLEI4gcaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zhFDXLiucb0jw5ocM5WBfmYO56xBkyhOvrdLEI4gcaE="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.u8jmjqbpf2.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u8jmjqbpf2"}, {"Name": "integrity", "Value": "sha256-Empz3gAMiN3myg3ccjDQebX8HchTX/L24SkroLi0g9A="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-maskedtextbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.fjcbrnxrtb.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13272"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fjcbrnxrtb"}, {"Name": "integrity", "Value": "sha256-O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sadf1bszs1-fjcbrnxrtb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000329272308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3036"}, {"Name": "ETag", "Value": "\"S2eq5BRVdDweA8Qh8cqQpTyaS3y0YxICEfj3YmsnEMM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13272"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O5u7z5EWrnEh1Bjma3ce459jSzptjJlzxEyGYI9ZdZ4="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-numerictextbox.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sadf1bszs1-fjcbrnxrtb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3036"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"S2eq5BRVdDweA8Qh8cqQpTyaS3y0YxICEfj3YmsnEMM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-S2eq5BRVdDweA8Qh8cqQpTyaS3y0YxICEfj3YmsnEMM="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.cg29fk2gn2.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-otp-input.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cg29fk2gn2"}, {"Name": "integrity", "Value": "sha256-VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\12khajnlde-cg29fk2gn2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001053740780"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "948"}, {"Name": "ETag", "Value": "\"I4KnvqwCsqAis1iU0aCb0LOTcgdZY7RbtjRtdbiVvEs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-otp-input.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VCyjOhxHJ2LT6cgXlulO8+71yn/kHGuuedwCdcpK30E="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-otp-input.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\12khajnlde-cg29fk2gn2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "948"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"I4KnvqwCsqAis1iU0aCb0LOTcgdZY7RbtjRtdbiVvEs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-I4KnvqwCsqAis1iU0aCb0LOTcgdZY7RbtjRtdbiVvEs="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\vbtp9tnch6-vteibwr79c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000438212095"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2281"}, {"Name": "ETag", "Value": "\"+dQJCfeGGJoL1/ZtpznF0DjWjmv9HNWHuZghME+P8yI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-rating.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\vbtp9tnch6-vteibwr79c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2281"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+dQJCfeGGJoL1/ZtpznF0DjWjmv9HNWHuZghME+P8yI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+dQJCfeGGJoL1/ZtpznF0DjWjmv9HNWHuZghME+P8yI="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.vteibwr79c.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-rating.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9276"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vteibwr79c"}, {"Name": "integrity", "Value": "sha256-bQnR/yZuV4LrG6d9PheSyPp6NELoZS38v4eMl3OJY00="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-rating.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sb4o3odzm8-x0gcv3gb9d.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000173852573"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5751"}, {"Name": "ETag", "Value": "\"BRJhIImstd4aM5hz+Qf0vpcz4ad7EIzblxrXY36nEXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-signature.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "23905"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\sb4o3odzm8-x0gcv3gb9d.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5751"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BRJhIImstd4aM5hz+Qf0vpcz4ad7EIzblxrXY36nEXc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BRJhIImstd4aM5hz+Qf0vpcz4ad7EIzblxrXY36nEXc="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.x0gcv3gb9d.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-signature.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "23905"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0gcv3gb9d"}, {"Name": "integrity", "Value": "sha256-IQcOYuSLquxfBprmRXjr++7kkRrTywoZpgltWlm7p+Y="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-signature.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jazegy34qu-lsufkzl165.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000160616768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6225"}, {"Name": "ETag", "Value": "\"98tM+/ygumT0DfanDWUi+Ev/kIr7s9BK8v2BEPJYS/Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-slider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31787"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jazegy34qu-lsufkzl165.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6225"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"98tM+/ygumT0DfanDWUi+Ev/kIr7s9BK8v2BEPJYS/Y=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-98tM+/ygumT0DfanDWUi+Ev/kIr7s9BK8v2BEPJYS/Y="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.lsufkzl165.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-slider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31787"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lsufkzl165"}, {"Name": "integrity", "Value": "sha256-pdkEr9JQW5kw+lDNXKPugKKMtjNal1xmZFlU2btAmwA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-slider.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.g9zntctz7a.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-speechtotext.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g9zntctz7a"}, {"Name": "integrity", "Value": "sha256-RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\kn04c7n2x3-g9zntctz7a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001103752759"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "905"}, {"Name": "ETag", "Value": "\"gbVH8zeaCoboxjY1YJN1D0r3ZqCJY45dNiw9hm/RY7I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-speechtotext.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2957"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RI8eE4/TatI0rCM6uzhJzYtiHbltA3ElhuQCl+w8o8s="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-speechtotext.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\kn04c7n2x3-g9zntctz7a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "905"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gbVH8zeaCoboxjY1YJN1D0r3ZqCJY45dNiw9hm/RY7I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gbVH8zeaCoboxjY1YJN1D0r3ZqCJY45dNiw9hm/RY7I="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.j3z817ikhe.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textarea.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j3z817ikhe"}, {"Name": "integrity", "Value": "sha256-r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ffhxaeu1yb-j3z817ikhe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001647446458"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "606"}, {"Name": "ETag", "Value": "\"P0Omx/MN0b5JoBSlcuJ4hegPFxyPLBgwwnauBQShyIU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textarea.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1202"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r6APH1ldaqAPCqm5bXoqVoNM5yA6eWXxc0Mdj1eSNME="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textarea.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ffhxaeu1yb-j3z817ikhe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "606"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P0Omx/MN0b5JoBSlcuJ4hegPFxyPLBgwwnauBQShyIU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P0Omx/MN0b5JoBSlcuJ4hegPFxyPLBgwwnauBQShyIU="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\pq6ds00kew-x6kv5h1gyc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000915750916"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1091"}, {"Name": "ETag", "Value": "\"PZn71QUo0qDByztlZYFVepsJWrX+ux3ZQDfQRf5QaHU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2772"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\pq6ds00kew-x6kv5h1gyc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1091"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PZn71QUo0qDByztlZYFVepsJWrX+ux3ZQDfQRf5QaHU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PZn71QUo0qDByztlZYFVepsJWrX+ux3ZQDfQRf5QaHU="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.x6kv5h1gyc.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-textbox.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2772"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x6kv5h1gyc"}, {"Name": "integrity", "Value": "sha256-B2+P7p2DiAyJEn3Q/owXLEDc3yV+MetMMi3y3yBI+O0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-textbox.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.1zm61t9neq.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-uploader.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "83883"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1zm61t9neq"}, {"Name": "integrity", "Value": "sha256-zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gk0sdbowrt-1zm61t9neq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000052512734"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19042"}, {"Name": "ETag", "Value": "\"4+L5ZLoTRFvukhCazVyEv9ffYYUHNkaCHJQGYUzThu8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\staticwebassets\\scripts\\sf-uploader.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "83883"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zT6o30K/iiclhFpAWc9LJyMoJKXoxMVqdTUwk5kN7z0="}]}, {"Route": "_content/Syncfusion.Blazor.Inputs/scripts/sf-uploader.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gk0sdbowrt-1zm61t9neq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19042"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4+L5ZLoTRFvukhCazVyEv9ffYYUHNkaCHJQGYUzThu8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4+L5ZLoTRFvukhCazVyEv9ffYYUHNkaCHJQGYUzThu8="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4l15mon31j-m9eje2li2o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000193348801"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5171"}, {"Name": "ETag", "Value": "\"yRtRpRHENr0dEYS5sUrDbfJeJ/yMAh+lhgkzRenIzSY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\staticwebassets\\scripts\\sf-listview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4l15mon31j-m9eje2li2o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5171"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yRtRpRHENr0dEYS5sUrDbfJeJ/yMAh+lhgkzRenIzSY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yRtRpRHENr0dEYS5sUrDbfJeJ/yMAh+lhgkzRenIzSY="}]}, {"Route": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.m9eje2li2o.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\staticwebassets\\scripts\\sf-listview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24435"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m9eje2li2o"}, {"Name": "integrity", "Value": "sha256-+rTI0r4ytrpcJzgb1Gh5udVTtZJA+B+CoQXcGkqilGU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Lists/scripts/sf-listview.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\vihrpo2kci-sm61wjcwzb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000252206810"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3964"}, {"Name": "ETag", "Value": "\"odNLJcNym9pg8uDFic/EHxQ4pvuePXpWVuh3JQa8jAw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\navigationsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19975"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\vihrpo2kci-sm61wjcwzb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3964"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"odNLJcNym9pg8uDFic/EHxQ4pvuePXpWVuh3JQa8jAw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-odNLJcNym9pg8uDFic/EHxQ4pvuePXpWVuh3JQa8jAw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.sm61wjcwzb.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\navigationsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19975"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sm61wjcwzb"}, {"Name": "integrity", "Value": "sha256-LBzVRtEtzM7ud/HB3/OccDs9DPvPJkPuIeAspU0hNZw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/navigationsbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\nr2uivr86d-mmw5aypzb0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311138768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "ETag", "Value": "\"V2e+/JiGf4fb0ioTvLpJ3+H50NChWorFYOLaygd99B8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-accordion.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12263"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\nr2uivr86d-mmw5aypzb0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"V2e+/JiGf4fb0ioTvLpJ3+H50NChWorFYOLaygd99B8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V2e+/JiGf4fb0ioTvLpJ3+H50NChWorFYOLaygd99B8="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.mmw5aypzb0.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-accordion.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12263"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mmw5aypzb0"}, {"Name": "integrity", "Value": "sha256-kml7tw/BtdDEDePKqeLwzvUI54nH6+PBP8SUBpfYqTQ="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-accordion.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.87p4l2g8lq.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4660"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87p4l2g8lq"}, {"Name": "integrity", "Value": "sha256-bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\0pcagc9na8-87p4l2g8lq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000634115409"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1576"}, {"Name": "ETag", "Value": "\"Le9djNuAk9kAcy1hJbYDkoUpjxp48lyZ1F3TrPbnLDg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4660"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bCr9SQ92XqnVmSOlP4lYEKNoXjb+QF0EWxySMJCC7NQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-breadcrumb.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\0pcagc9na8-87p4l2g8lq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1576"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Le9djNuAk9kAcy1hJbYDkoUpjxp48lyZ1F3TrPbnLDg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Le9djNuAk9kAcy1hJbYDkoUpjxp48lyZ1F3TrPbnLDg="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\9ispkuv7ij-neeiqevn6f.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000520291363"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1921"}, {"Name": "ETag", "Value": "\"9WDXMEXmETVRBJl0uX/Bs1HEvvCl6Vx/u+CGl8QiEdA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-carousel.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\9ispkuv7ij-neeiqevn6f.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1921"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9WDXMEXmETVRBJl0uX/Bs1HEvvCl6Vx/u+CGl8QiEdA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9WDXMEXmETVRBJl0uX/Bs1HEvvCl6Vx/u+CGl8QiEdA="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.neeiqevn6f.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-carousel.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6563"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "neeiqevn6f"}, {"Name": "integrity", "Value": "sha256-keSGByD2KZjoteU5ud7yyZ8Y/6bt069G4MTlk2N19UA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-carousel.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\tqhdg3t9bq-uqwhurys37.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000213492741"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4683"}, {"Name": "ETag", "Value": "\"Gtsk7rpBOXY/T91Lfo//jGWJwsaTzFMSXkayF3z5mc0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-contextmenu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\tqhdg3t9bq-uqwhurys37.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Gtsk7rpBOXY/T91Lfo//jGWJwsaTzFMSXkayF3z5mc0=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Gtsk7rpBOXY/T91Lfo//jGWJwsaTzFMSXkayF3z5mc0="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.uqwhurys37.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-contextmenu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18233"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uqwhurys37"}, {"Name": "integrity", "Value": "sha256-Bgob3YMlkmNhWfJTRyuIrxKX8xfvs2dWjYYI/ZFjQWI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-contextmenu.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.3yq1pja1s9.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "30439"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3yq1pja1s9"}, {"Name": "integrity", "Value": "sha256-qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\eejr38ihun-3yq1pja1s9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000175592625"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5694"}, {"Name": "ETag", "Value": "\"Cvz4NjqBVSvecNfBzkOWx3KcKIP54vaN4tSFweOJDEg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30439"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qwNRQufXqPMxd4vY/NuCuGoVfordn1tT45jwypzjVPw="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-dropdowntree.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\eejr38ihun-3yq1pja1s9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5694"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Cvz4NjqBVSvecNfBzkOWx3KcKIP54vaN4tSFweOJDEg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cvz4NjqBVSvecNfBzkOWx3KcKIP54vaN4tSFweOJDEg="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.7u9umw6dst.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-menu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15332"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7u9umw6dst"}, {"Name": "integrity", "Value": "sha256-sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\djau0kle4k-7u9umw6dst.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000257334020"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3885"}, {"Name": "ETag", "Value": "\"/I/HyINbnrlgHx0KLaDGVMMmqf7t4KwJbp2GX+T3SyQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-menu.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15332"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sXzOZNI1Ie6hDdvGJrX0BquZZPXXq0Bv97CTq47atto="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-menu.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\djau0kle4k-7u9umw6dst.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3885"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/I/HyINbnrlgHx0KLaDGVMMmqf7t4KwJbp2GX+T3SyQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/I/HyINbnrlgHx0KLaDGVMMmqf7t4KwJbp2GX+T3SyQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.a20odbfi5s.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-pager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9943"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a20odbfi5s"}, {"Name": "integrity", "Value": "sha256-Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\mfqtlt087y-a20odbfi5s.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "ETag", "Value": "\"UW6QYLlXfX7w5IONRLw/CQ2qpM8oeFBnYeuGLHpGNlQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-pager.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9943"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ur/qc8t1L2VRv2uCVplTlYIwFm3LNb+mi4Et72Le5tc="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-pager.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\mfqtlt087y-a20odbfi5s.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UW6QYLlXfX7w5IONRLw/CQ2qpM8oeFBnYeuGLHpGNlQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UW6QYLlXfX7w5IONRLw/CQ2qpM8oeFBnYeuGLHpGNlQ="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.aulwboaixq.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-sidebar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10456"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aulwboaixq"}, {"Name": "integrity", "Value": "sha256-kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ukontf0to4-aulwboaixq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000363768643"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2748"}, {"Name": "ETag", "Value": "\"hyCTJz97AMDNSYhDtddN/DP5eNRRkMhp1bFmYCaKgSY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-sidebar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10456"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kqEw/0ZITjtpaynG5rFBDx6ClWL+AtHIConWXp8kbyk="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-sidebar.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ukontf0to4-aulwboaixq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2748"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hyCTJz97AMDNSYhDtddN/DP5eNRRkMhp1bFmYCaKgSY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hyCTJz97AMDNSYhDtddN/DP5eNRRkMhp1bFmYCaKgSY="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.3jpai5pxoj.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-stepper.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14111"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3jpai5pxoj"}, {"Name": "integrity", "Value": "sha256-QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\grozcvm4ej-3jpai5pxoj.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000297619048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3359"}, {"Name": "ETag", "Value": "\"fgwf80QQNF74t94ouzAmCSu8d51wR4RG0XHLL1Haw1U=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-stepper.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14111"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QQpTfpvpMfLfkslw4DDKS2BKf+GXBJ3Pz56MDwd9CAo="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-stepper.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\grozcvm4ej-3jpai5pxoj.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3359"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fgwf80QQNF74t94ouzAmCSu8d51wR4RG0XHLL1Haw1U=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fgwf80QQNF74t94ouzAmCSu8d51wR4RG0XHLL1Haw1U="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jhhm3fquz3-xf4d1dg01c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000130191381"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7680"}, {"Name": "ETag", "Value": "\"SgAd2LpYQZKAptnwN2vg6BeU5df5r+ezD4HDEJr9GwY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-tab.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "34125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\jhhm3fquz3-xf4d1dg01c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7680"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SgAd2LpYQZKAptnwN2vg6BeU5df5r+ezD4HDEJr9GwY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SgAd2LpYQZKAptnwN2vg6BeU5df5r+ezD4HDEJr9GwY="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.xf4d1dg01c.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-tab.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "34125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xf4d1dg01c"}, {"Name": "integrity", "Value": "sha256-vUxvBrVAE0t980bEX5/U4moSw9YOvDfN6hX/JmB1NSk="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-tab.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2m61bm772c-rb6ei14cmq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000107492207"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9302"}, {"Name": "ETag", "Value": "\"q+ed+iEDLNjMLxOIE3yFCDYfkvQS1MU/MLj/yoWJXwo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-toolbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "41177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2m61bm772c-rb6ei14cmq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9302"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"q+ed+iEDLNjMLxOIE3yFCDYfkvQS1MU/MLj/yoWJXwo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-q+ed+iEDLNjMLxOIE3yFCDYfkvQS1MU/MLj/yoWJXwo="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.rb6ei14cmq.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-toolbar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "41177"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rb6ei14cmq"}, {"Name": "integrity", "Value": "sha256-ebLIdtbzJbDWvlhyOJmxPGnu+YZPKM8ClJ8N1Lrc4pM="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-toolbar.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.4yr0zuexvq.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-treeview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "49873"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yr0zuexvq"}, {"Name": "integrity", "Value": "sha256-fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\diku15o4fe-4yr0zuexvq.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083857442"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11924"}, {"Name": "ETag", "Value": "\"NQZiTysXepP1oHYxdFf6HZoL3fUKe0g9XT2/Ej+VeWc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\staticwebassets\\scripts\\sf-treeview.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "49873"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fOiAxarOveAlR4DDaxmR5nNL8F1SzHaBzHpCwWj+jKc="}]}, {"Route": "_content/Syncfusion.Blazor.Navigations/scripts/sf-treeview.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\diku15o4fe-4yr0zuexvq.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11924"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"NQZiTysXepP1oHYxdFf6HZoL3fUKe0g9XT2/Ej+VeWc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NQZiTysXepP1oHYxdFf6HZoL3fUKe0g9XT2/Ej+VeWc="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\9zg8dqxfdk-viv3nqn9op.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000451059991"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2216"}, {"Name": "ETag", "Value": "\"OsMVazHqMjAjwfwXwli8LGa6lwXPJCyNsd5gKONG088=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\staticwebassets\\scripts\\sf-toast.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\9zg8dqxfdk-viv3nqn9op.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2216"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OsMVazHqMjAjwfwXwli8LGa6lwXPJCyNsd5gKONG088=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OsMVazHqMjAjwfwXwli8LGa6lwXPJCyNsd5gKONG088="}]}, {"Route": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.viv3nqn9op.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\staticwebassets\\scripts\\sf-toast.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "viv3nqn9op"}, {"Name": "integrity", "Value": "sha256-qW5k4N8pxtyk95Z8uYJ/SP9QCLe8/iGWtlGFaV4zkD0="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Notifications/scripts/sf-toast.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2lc9f2kwg-xddooubi4o.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000174094708"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5743"}, {"Name": "ETag", "Value": "\"9nV3uGup69tcE0TavB1jPfRIaUh5AIzerTrukcCqLfg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-dialog.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "24870"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2lc9f2kwg-xddooubi4o.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5743"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9nV3uGup69tcE0TavB1jPfRIaUh5AIzerTrukcCqLfg=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9nV3uGup69tcE0TavB1jPfRIaUh5AIzerTrukcCqLfg="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.xddooubi4o.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-dialog.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "24870"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xddooubi4o"}, {"Name": "integrity", "Value": "sha256-OyY+gGLhmNTL3E25UeQxPTPvQmQm2yynNrWaezWHnfs="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Popups/scripts/sf-dialog.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.e2u6q3p1lh.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-tooltip.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "32214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e2u6q3p1lh"}, {"Name": "integrity", "Value": "sha256-SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\3spo2eianh-e2u6q3p1lh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000136855070"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7306"}, {"Name": "ETag", "Value": "\"663pRccky4jzFU7mEhDEruVWQwL74Cq7hAgO+jjHzsc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\staticwebassets\\scripts\\sf-tooltip.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "32214"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SgyRzYHM7Fim4rHpVCNIgPkWHbm1M8C9jFKPww03nM8="}]}, {"Route": "_content/Syncfusion.Blazor.Popups/scripts/sf-tooltip.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\3spo2eianh-e2u6q3p1lh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7306"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"663pRccky4jzFU7mEhDEruVWQwL74Cq7hAgO+jjHzsc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-663pRccky4jzFU7mEhDEruVWQwL74Cq7hAgO+jjHzsc="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.jqs3n24tf7.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\sf-spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jqs3n24tf7"}, {"Name": "integrity", "Value": "sha256-O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2ndbdiwf0k-jqs3n24tf7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002118644068"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "471"}, {"Name": "ETag", "Value": "\"bOLNw1bgUPqHPOUK4WRLzDzyHxgE6es4bGpjBwfjTw8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\sf-spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "889"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O/MZPfxDgjURNwnPzHlEpiBanDhX3icRqMYzHO2ofgU="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/sf-spinner.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2ndbdiwf0k-jqs3n24tf7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "471"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bOLNw1bgUPqHPOUK4WRLzDzyHxgE6es4bGpjBwfjTw8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bOLNw1bgUPqHPOUK4WRLzDzyHxgE6es4bGpjBwfjTw8="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.4h59cheqig.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11380"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4h59cheqig"}, {"Name": "integrity", "Value": "sha256-CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\g591birpkc-4h59cheqig.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000295420975"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3384"}, {"Name": "ETag", "Value": "\"Tv00ASBTMUGgKKFdiZ0djWPDf6VTnpz7xusIVckSIkc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\staticwebassets\\scripts\\spinner.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11380"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CKwJFYycXsCxdE7+OjnD7kuJ+BIyQMAIUwdo0b7UxlE="}]}, {"Route": "_content/Syncfusion.Blazor.Spinner/scripts/spinner.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\g591birpkc-4h59cheqig.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3384"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Tv00ASBTMUGgKKFdiZ0djWPDf6VTnpz7xusIVckSIkc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tv00ASBTMUGgKKFdiZ0djWPDf6VTnpz7xusIVckSIkc="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\bm0n96jk7v-y4pke33q7n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000398247710"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2510"}, {"Name": "ETag", "Value": "\"QU7kJBL8MSk41KRAYHPDylFkQgmBSTRJXxXvDo8VF/I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\bm0n96jk7v-y4pke33q7n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2510"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QU7kJBL8MSk41KRAYHPDylFkQgmBSTRJXxXvDo8VF/I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QU7kJBL8MSk41KRAYHPDylFkQgmBSTRJXxXvDo8VF/I="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.y4pke33q7n.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y4pke33q7n"}, {"Name": "integrity", "Value": "sha256-QiaoqSrREBZ44y65Pl5gH8nNhQAvbdVaHGZ//QRhgLA="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.SplitButtons/scripts/sf-drop-down-button.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.aphbptuiu1.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3706"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aphbptuiu1"}, {"Name": "integrity", "Value": "sha256-XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI="}, {"Name": "label", "Value": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js"}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\p2zgtah1ds-aphbptuiu1.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000691085003"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1446"}, {"Name": "ETag", "Value": "\"6UU37OjaY6/vrGU+s3xH0DO1ehrN+ZyLdS5we1JzE6g=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3706"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 09:45:50 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XtvDEfhnyXI9rQd0y31ijIkq1/N1ZSgS5D0zNehJ6JI="}]}, {"Route": "_content/Syncfusion.Blazor.SplitButtons/scripts/splitbuttonsbase.min.js.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\p2zgtah1ds-aphbptuiu1.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1446"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6UU37OjaY6/vrGU+s3xH0DO1ehrN+ZyLdS5we1JzE6g=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 14:12:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6UU37OjaY6/vrGU+s3xH0DO1ehrN+ZyLdS5we1JzE6g="}]}, {"Route": "Clients.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2dj3icwxz-pbnxt20j60.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000449034576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2226"}, {"Name": "ETag", "Value": "\"4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk="}]}, {"Route": "Clients.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Clients.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13575"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk="}]}, {"Route": "Clients.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2dj3icwxz-pbnxt20j60.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk="}]}, {"Route": "Clients.pbnxt20j60.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2dj3icwxz-pbnxt20j60.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000449034576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2226"}, {"Name": "ETag", "Value": "\"4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbnxt20j60"}, {"Name": "label", "Value": "Clients.html"}, {"Name": "integrity", "Value": "sha256-VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk="}]}, {"Route": "Clients.pbnxt20j60.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Clients.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13575"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbnxt20j60"}, {"Name": "label", "Value": "Clients.html"}, {"Name": "integrity", "Value": "sha256-VTnJJ+/SLv9Cc+Jb8ThLQMHyUOOkCwQMagtRukY/NOk="}]}, {"Route": "Clients.pbnxt20j60.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\k2dj3icwxz-pbnxt20j60.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbnxt20j60"}, {"Name": "label", "Value": "Clients.html.gz"}, {"Name": "integrity", "Value": "sha256-4cftgxXhF0GaOooYwmvGkf/QjUDUIuBoIRnYBrceXUk="}]}, {"Route": "Dashboard.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gyys9ii1lu-ot006j5kmx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000176149375"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5676"}, {"Name": "ETag", "Value": "\"KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw="}]}, {"Route": "Dashboard.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Dashboard.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24921"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw="}]}, {"Route": "Dashboard.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gyys9ii1lu-ot006j5kmx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5676"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764="}]}, {"Route": "Dashboard.ot006j5kmx.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gyys9ii1lu-ot006j5kmx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000176149375"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5676"}, {"Name": "ETag", "Value": "\"KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ot006j5kmx"}, {"Name": "label", "Value": "Dashboard.html"}, {"Name": "integrity", "Value": "sha256-LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw="}]}, {"Route": "Dashboard.ot006j5kmx.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Dashboard.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24921"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ot006j5kmx"}, {"Name": "label", "Value": "Dashboard.html"}, {"Name": "integrity", "Value": "sha256-LEY2NYiHZCNOCZbXfOLcGehO8Li2oFDr3myJssIWGLw="}]}, {"Route": "Dashboard.ot006j5kmx.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\gyys9ii1lu-ot006j5kmx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5676"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ot006j5kmx"}, {"Name": "label", "Value": "Dashboard.html.gz"}, {"Name": "integrity", "Value": "sha256-KbriHx+4CsDdUNW5M0OW9gNCBSogzfq+stnGq41L764="}]}, {"Route": "favicon.ico", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\1nafdd6chw-tgup6kq3m2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000012424830"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80483"}, {"Name": "ETag", "Value": "\"pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:37:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}]}, {"Route": "favicon.ico", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81853"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}]}, {"Route": "favicon.ico.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\1nafdd6chw-tgup6kq3m2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80483"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:37:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I="}]}, {"Route": "favicon.tgup6kq3m2.ico", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\1nafdd6chw-tgup6kq3m2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000012424830"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80483"}, {"Name": "ETag", "Value": "\"pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:37:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgup6kq3m2"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}]}, {"Route": "favicon.tgup6kq3m2.ico", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81853"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgup6kq3m2"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}]}, {"Route": "favicon.tgup6kq3m2.ico.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\1nafdd6chw-tgup6kq3m2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80483"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:37:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgup6kq3m2"}, {"Name": "label", "Value": "favicon.ico.gz"}, {"Name": "integrity", "Value": "sha256-pdHMhwZMziKEh2OdAfoVVwH3vYMLzdbIOWjewv0Uw4I="}]}, {"Route": "Gestion de stock.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dtw28fmir4-wl6n5gvn3c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000386697602"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2585"}, {"Name": "ETag", "Value": "\"ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM="}]}, {"Route": "Gestion de stock.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Gestion de stock.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11187"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM="}]}, {"Route": "Gestion de stock.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dtw28fmir4-wl6n5gvn3c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2585"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE="}]}, {"Route": "Gestion de stock.wl6n5gvn3c.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dtw28fmir4-wl6n5gvn3c.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000386697602"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2585"}, {"Name": "ETag", "Value": "\"ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl6n5gvn3c"}, {"Name": "label", "Value": "Gestion de stock.html"}, {"Name": "integrity", "Value": "sha256-zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM="}]}, {"Route": "Gestion de stock.wl6n5gvn3c.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Gestion de stock.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11187"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl6n5gvn3c"}, {"Name": "label", "Value": "Gestion de stock.html"}, {"Name": "integrity", "Value": "sha256-zGkX5tYN3vDOs2P2onZzce92Xr5CNc/3ZQHZPLI2RUM="}]}, {"Route": "Gestion de stock.wl6n5gvn3c.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\dtw28fmir4-wl6n5gvn3c.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2585"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wl6n5gvn3c"}, {"Name": "label", "Value": "Gestion de stock.html.gz"}, {"Name": "integrity", "Value": "sha256-ll+SaFvk65+xaGqO3VBXMdQ6EC6bCTUmFkcgEKumxoE="}]}, {"Route": "Home.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\yb1j5ju6dz-o0kmq4r1qc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000197083169"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5073"}, {"Name": "ETag", "Value": "\"h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc="}]}, {"Route": "Home.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Home.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22265"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc="}]}, {"Route": "Home.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\yb1j5ju6dz-o0kmq4r1qc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk="}]}, {"Route": "Home.o0kmq4r1qc.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\yb1j5ju6dz-o0kmq4r1qc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000197083169"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5073"}, {"Name": "ETag", "Value": "\"h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o0kmq4r1qc"}, {"Name": "label", "Value": "Home.html"}, {"Name": "integrity", "Value": "sha256-KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc="}]}, {"Route": "Home.o0kmq4r1qc.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Home.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22265"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o0kmq4r1qc"}, {"Name": "label", "Value": "Home.html"}, {"Name": "integrity", "Value": "sha256-KBdM7YQOqH+6E7uLzmbBz2GGQTrsbgG5bES6mGGotrc="}]}, {"Route": "Home.o0kmq4r1qc.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\yb1j5ju6dz-o0kmq4r1qc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5073"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o0kmq4r1qc"}, {"Name": "label", "Value": "Home.html.gz"}, {"Name": "integrity", "Value": "sha256-h9w0vaejaU7/3AzQ5kPY0IoRrpVXJak3QxYAlt9s3Dk="}]}, {"Route": "images/arrow-303116_1280.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\arrow-303116_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "124362"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zTW0FbBAUvNRJ7rSWsljWJp3YEpZHnlwxZIONGLozak=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zTW0FbBAUvNRJ7rSWsljWJp3YEpZHnlwxZIONGLozak="}]}, {"Route": "images/arrow-303116_1280.pteby9jfp2.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\arrow-303116_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "124362"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zTW0FbBAUvNRJ7rSWsljWJp3YEpZHnlwxZIONGLozak=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pteby9jfp2"}, {"Name": "label", "Value": "images/arrow-303116_1280.png"}, {"Name": "integrity", "Value": "sha256-zTW0FbBAUvNRJ7rSWsljWJp3YEpZHnlwxZIONGLozak="}]}, {"Route": "images/brain-1710293_1280.lwkossosp8.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\brain-1710293_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "197178"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1Yvas6AJovw5WS+lKhl+8/axHtWbnmSKyZI6YpnqHUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lwkossosp8"}, {"Name": "label", "Value": "images/brain-1710293_1280.png"}, {"Name": "integrity", "Value": "sha256-1Yvas6AJovw5WS+lKhl+8/axHtWbnmSKyZI6YpnqHUk="}]}, {"Route": "images/brain-1710293_1280.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\brain-1710293_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "197178"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1Yvas6AJovw5WS+lKhl+8/axHtWbnmSKyZI6YpnqHUk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1Yvas6AJovw5WS+lKhl+8/axHtWbnmSKyZI6YpnqHUk="}]}, {"Route": "images/ChatGPT Image 22 juil. 2025, 13_51_40.n6eglj16dh.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\ChatGPT Image 22 juil. 2025, 13_51_40.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1800368"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z5xlFb1NMh9wVMGXpz+bL1/WfFOSvLDQziJqQUOaTPA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n6eglj16dh"}, {"Name": "label", "Value": "images/ChatGPT Image 22 juil. 2025, 13_51_40.png"}, {"Name": "integrity", "Value": "sha256-z5xlFb1NMh9wVMGXpz+bL1/WfFOSvLDQziJqQUOaTPA="}]}, {"Route": "images/ChatGPT Image 22 juil. 2025, 13_51_40.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\ChatGPT Image 22 juil. 2025, 13_51_40.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1800368"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z5xlFb1NMh9wVMGXpz+bL1/WfFOSvLDQziJqQUOaTPA=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z5xlFb1NMh9wVMGXpz+bL1/WfFOSvLDQziJqQUOaTPA="}]}, {"Route": "images/ChatGPT Image 23 juil. 2025, 12_11_51.kx17dmg14z.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\ChatGPT Image 23 juil. 2025, 12_11_51.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1604670"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eHUky4q4kO+lhyvvCanyBTSvezc+uIYnRSFwTifWXNk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kx17dmg14z"}, {"Name": "label", "Value": "images/ChatGPT Image 23 juil. 2025, 12_11_51.png"}, {"Name": "integrity", "Value": "sha256-eHUky4q4kO+lhyvvCanyBTSvezc+uIYnRSFwTifWXNk="}]}, {"Route": "images/ChatGPT Image 23 juil. 2025, 12_11_51.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\ChatGPT Image 23 juil. 2025, 12_11_51.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1604670"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eHUky4q4kO+lhyvvCanyBTSvezc+uIYnRSFwTifWXNk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eHUky4q4kO+lhyvvCanyBTSvezc+uIYnRSFwTifWXNk="}]}, {"Route": "images/computer-8070002_1280.jpg", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\computer-8070002_1280.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "395581"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"8fKglCKLBooRwr+kwcg3XbfBusiBzb6rpclvym9uZx4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8fKglCKLBooRwr+kwcg3XbfBusiBzb6rpclvym9uZx4="}]}, {"Route": "images/computer-8070002_1280.xcmnicqqpw.jpg", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\computer-8070002_1280.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "395581"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"8fKglCKLBooRwr+kwcg3XbfBusiBzb6rpclvym9uZx4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xcmnicqqpw"}, {"Name": "label", "Value": "images/computer-8070002_1280.jpg"}, {"Name": "integrity", "Value": "sha256-8fKglCKLBooRwr+kwcg3XbfBusiBzb6rpclvym9uZx4="}]}, {"Route": "images/Engineer.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\Engineer.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "688006"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zpe06c4e14cI6tozLMgbHe45TjLdgErciBtlTBOpaTY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zpe06c4e14cI6tozLMgbHe45TjLdgErciBtlTBOpaTY="}]}, {"Route": "images/Engineer.qhl0j27a0c.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\Engineer.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "688006"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zpe06c4e14cI6tozLMgbHe45TjLdgErciBtlTBOpaTY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qhl0j27a0c"}, {"Name": "label", "Value": "images/Engineer.png"}, {"Name": "integrity", "Value": "sha256-zpe06c4e14cI6tozLMgbHe45TjLdgErciBtlTBOpaTY="}]}, {"Route": "images/information-275708_1280.4zd9fdnpjz.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\information-275708_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1442675"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"AN7w8KNMJxOT+Hn6kuo+8pqOhByhnEvPEhooXwSlOqE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4zd9fdnpjz"}, {"Name": "label", "Value": "images/information-275708_1280.png"}, {"Name": "integrity", "Value": "sha256-AN7w8KNMJxOT+Hn6kuo+8pqOhByhnEvPEhooXwSlOqE="}]}, {"Route": "images/information-275708_1280.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\information-275708_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1442675"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"AN7w8KNMJxOT+Hn6kuo+8pqOhByhnEvPEhooXwSlOqE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AN7w8KNMJxOT+Hn6kuo+8pqOhByhnEvPEhooXwSlOqE="}]}, {"Route": "images/investigation-9604083_1280-removebg-preview.d4djnk86q1.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\investigation-9604083_1280-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "119530"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"LRDM8biSAIMVifVe8b8ZB1sj/Idyw2MtKgoiQDJJ8kY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d4djnk86q1"}, {"Name": "label", "Value": "images/investigation-9604083_1280-removebg-preview.png"}, {"Name": "integrity", "Value": "sha256-LRDM8biSAIMVifVe8b8ZB1sj/Idyw2MtKgoiQDJJ8kY="}]}, {"Route": "images/investigation-9604083_1280-removebg-preview.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\investigation-9604083_1280-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "119530"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"LRDM8biSAIMVifVe8b8ZB1sj/Idyw2MtKgoiQDJJ8kY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LRDM8biSAIMVifVe8b8ZB1sj/Idyw2MtKgoiQDJJ8kY="}]}, {"Route": "images/lightbulb-1344763_1280.dbjrebzixc.jpg", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\lightbulb-1344763_1280.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "59824"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"d9ufWX/mt0HqG3+bUtAJdqQTULOCzPyh+80J5PszE2s=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dbjrebzixc"}, {"Name": "label", "Value": "images/lightbulb-1344763_1280.jpg"}, {"Name": "integrity", "Value": "sha256-d9ufWX/mt0HqG3+bUtAJdqQTULOCzPyh+80J5PszE2s="}]}, {"Route": "images/lightbulb-1344763_1280.jpg", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\lightbulb-1344763_1280.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "59824"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"d9ufWX/mt0HqG3+bUtAJdqQTULOCzPyh+80J5PszE2s=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d9ufWX/mt0HqG3+bUtAJdqQTULOCzPyh+80J5PszE2s="}]}, {"Route": "images/man-8106958_1280-removebg-preview.9xuud5qfxm.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\man-8106958_1280-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "138762"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"DbleI21Wx+knF4v7+U6f4nT00//UBOrY92+h8Q2J7ig=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9xuud5qfxm"}, {"Name": "label", "Value": "images/man-8106958_1280-removebg-preview.png"}, {"Name": "integrity", "Value": "sha256-DbleI21Wx+knF4v7+U6f4nT00//UBOrY92+h8Q2J7ig="}]}, {"Route": "images/man-8106958_1280-removebg-preview.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\man-8106958_1280-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "138762"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"DbleI21Wx+knF4v7+U6f4nT00//UBOrY92+h8Q2J7ig=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DbleI21Wx+knF4v7+U6f4nT00//UBOrY92+h8Q2J7ig="}]}, {"Route": "images/man-8106958_1280.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\man-8106958_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "219120"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ZSmo6T2OOqDI7mfPOj/HEMcFGlFet9PfXZfLT4XHKNk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZSmo6T2OOqDI7mfPOj/HEMcFGlFet9PfXZfLT4XHKNk="}]}, {"Route": "images/man-8106958_1280.v39exk6n1y.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\man-8106958_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "219120"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ZSmo6T2OOqDI7mfPOj/HEMcFGlFet9PfXZfLT4XHKNk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v39exk6n1y"}, {"Name": "label", "Value": "images/man-8106958_1280.png"}, {"Name": "integrity", "Value": "sha256-ZSmo6T2OOqDI7mfPOj/HEMcFGlFet9PfXZfLT4XHKNk="}]}, {"Route": "images/man-9553723_1280.29ipq6tdix.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\man-9553723_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "252779"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"WpXvLbzqc9ipSXL+34vK51tD1KSU9mshn+0uVMgNwo8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "29ipq6tdix"}, {"Name": "label", "Value": "images/man-9553723_1280.png"}, {"Name": "integrity", "Value": "sha256-WpXvLbzqc9ipSXL+34vK51tD1KSU9mshn+0uVMgNwo8="}]}, {"Route": "images/man-9553723_1280.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\man-9553723_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "252779"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"WpXvLbzqc9ipSXL+34vK51tD1KSU9mshn+0uVMgNwo8=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WpXvLbzqc9ipSXL+34vK51tD1KSU9mshn+0uVMgNwo8="}]}, {"Route": "images/profile-42914_1280.cf9t04yl97.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\profile-42914_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17787"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CDvQqAOxRqn/Pj3p1V2Wn8nHEUC1hyAJGFRAJ1tmS+E=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cf9t04yl97"}, {"Name": "label", "Value": "images/profile-42914_1280.png"}, {"Name": "integrity", "Value": "sha256-CDvQqAOxRqn/Pj3p1V2Wn8nHEUC1hyAJGFRAJ1tmS+E="}]}, {"Route": "images/profile-42914_1280.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\profile-42914_1280.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17787"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"CDvQqAOxRqn/Pj3p1V2Wn8nHEUC1hyAJGFRAJ1tmS+E=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CDvQqAOxRqn/Pj3p1V2Wn8nHEUC1hyAJGFRAJ1tmS+E="}]}, {"Route": "images/Profile.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\Profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}]}, {"Route": "images/Profile.tgup6kq3m2.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\Profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81853"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tgup6kq3m2"}, {"Name": "label", "Value": "images/Profile.png"}, {"Name": "integrity", "Value": "sha256-g6N+aT07unf5ZfD/ncg9P/rk/+fSEc2+wcrdd+AG0Mc="}]}, {"Route": "images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.gkgbygsbb1.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "208301"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oEpb678wDT1h12J+yOnfWrskxY2nVKeebLzb8Jwak6w=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gkgbygsbb1"}, {"Name": "label", "Value": "images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png"}, {"Name": "integrity", "Value": "sha256-oEpb678wDT1h12J+yOnfWrskxY2nVKeebLzb8Jwak6w="}]}, {"Route": "images/puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\images\\puzzle-creative-Tg3GFhqXpUo-unsplash-removebg-preview.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "208301"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"oEpb678wDT1h12J+yOnfWrskxY2nVKeebLzb8Jwak6w=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oEpb678wDT1h12J+yOnfWrskxY2nVKeebLzb8Jwak6w="}]}, {"Route": "Interventions.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\od31b7jcnl-xqeb31wrq2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000609756098"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1639"}, {"Name": "ETag", "Value": "\"osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY="}]}, {"Route": "Interventions.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Interventions.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5682"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY="}]}, {"Route": "Interventions.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\od31b7jcnl-xqeb31wrq2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1639"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs="}]}, {"Route": "Interventions.xqeb31wrq2.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\od31b7jcnl-xqeb31wrq2.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000609756098"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1639"}, {"Name": "ETag", "Value": "\"osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xqeb31wrq2"}, {"Name": "label", "Value": "Interventions.html"}, {"Name": "integrity", "Value": "sha256-l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY="}]}, {"Route": "Interventions.xqeb31wrq2.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Interventions.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5682"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xqeb31wrq2"}, {"Name": "label", "Value": "Interventions.html"}, {"Name": "integrity", "Value": "sha256-l5m7BJfoFSmNikiazc1RAhzwYIuyYcKqAKU8v9cx4hY="}]}, {"Route": "Interventions.xqeb31wrq2.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\od31b7jcnl-xqeb31wrq2.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1639"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xqeb31wrq2"}, {"Name": "label", "Value": "Interventions.html.gz"}, {"Name": "integrity", "Value": "sha256-osdokJoly1J/bhjU/5yWi5nDbM/Uu/ToRLnhB5NlFvs="}]}, {"Route": "Login.7xmio610hh.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\90k8s08yp3-7xmio610hh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000636537237"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1570"}, {"Name": "ETag", "Value": "\"okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xmio610hh"}, {"Name": "label", "Value": "Login.html"}, {"Name": "integrity", "Value": "sha256-dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk="}]}, {"Route": "Login.7xmio610hh.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5210"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xmio610hh"}, {"Name": "label", "Value": "Login.html"}, {"Name": "integrity", "Value": "sha256-dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk="}]}, {"Route": "Login.7xmio610hh.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\90k8s08yp3-7xmio610hh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1570"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7xmio610hh"}, {"Name": "label", "Value": "Login.html.gz"}, {"Name": "integrity", "Value": "sha256-okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4="}]}, {"Route": "Login.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\90k8s08yp3-7xmio610hh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000636537237"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1570"}, {"Name": "ETag", "Value": "\"okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk="}]}, {"Route": "Login.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5210"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dbv3QgCB+5CAdBqMt7TZhe1JReUaECZSvbcqd0s4Lxk="}]}, {"Route": "Login.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\90k8s08yp3-7xmio610hh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1570"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-okw+KgBGyGc6p9a4x6R+OJxBpXWhCRhFxcpVc9IP//4="}]}, {"Route": "Mission.2k64m4xzz5.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ji856uyn4d-2k64m4xzz5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301932367"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3311"}, {"Name": "ETag", "Value": "\"EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"MjDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2k64m4xzz5"}, {"Name": "label", "Value": "Mission.html"}, {"Name": "integrity", "Value": "sha256-<PERSON>jDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE="}]}, {"Route": "Mission.2k64m4xzz5.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Mission.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17909"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"MjDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2k64m4xzz5"}, {"Name": "label", "Value": "Mission.html"}, {"Name": "integrity", "Value": "sha256-<PERSON>jDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE="}]}, {"Route": "Mission.2k64m4xzz5.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ji856uyn4d-2k64m4xzz5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3311"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2k64m4xzz5"}, {"Name": "label", "Value": "Mission.html.gz"}, {"Name": "integrity", "Value": "sha256-EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw="}]}, {"Route": "Mission.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ji856uyn4d-2k64m4xzz5.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000301932367"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3311"}, {"Name": "ETag", "Value": "\"EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"MjDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-<PERSON>jDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE="}]}, {"Route": "Mission.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Mission.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17909"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"MjDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-<PERSON>jDslGab7jlgjGUP76now67kKNCqCluJk4eR7a4bdaE="}]}, {"Route": "Mission.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\ji856uyn4d-2k64m4xzz5.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3311"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EREfqoKUF9JRCZWKmMwvqUwIDH7RKmjHzr8o8QD++iw="}]}, {"Route": "MonCompte.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2pse2bi12y-vg9bitalgh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000427167877"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2340"}, {"Name": "ETag", "Value": "\"7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk="}]}, {"Route": "MonCompte.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\MonCompte.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8845"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk="}]}, {"Route": "MonCompte.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2pse2bi12y-vg9bitalgh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2340"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI="}]}, {"Route": "MonCompte.vg9bitalgh.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2pse2bi12y-vg9bitalgh.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000427167877"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2340"}, {"Name": "ETag", "Value": "\"7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vg9bitalgh"}, {"Name": "label", "Value": "MonCompte.html"}, {"Name": "integrity", "Value": "sha256-UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk="}]}, {"Route": "MonCompte.vg9bitalgh.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\MonCompte.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8845"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vg9bitalgh"}, {"Name": "label", "Value": "MonCompte.html"}, {"Name": "integrity", "Value": "sha256-UTUwsgod7iO0DY63lOv723cLdnWcHR8Mcts8Q1nLCPk="}]}, {"Route": "MonCompte.vg9bitalgh.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\2pse2bi12y-vg9bitalgh.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2340"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vg9bitalgh"}, {"Name": "label", "Value": "MonCompte.html.gz"}, {"Name": "integrity", "Value": "sha256-7uKW1mI0C21DRLbF1nP1ukC6vWjiCBlFN2B8sbeYGWI="}]}, {"Route": "Rapport.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5a6tfv8f1g-pdyi0fsnlt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000461041955"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "ETag", "Value": "\"Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo="}]}, {"Route": "Rapport.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Rapport.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10441"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo="}]}, {"Route": "Rapport.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5a6tfv8f1g-pdyi0fsnlt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU="}]}, {"Route": "Rapport.pdyi0fsnlt.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5a6tfv8f1g-pdyi0fsnlt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000461041955"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "ETag", "Value": "\"Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pdyi0fsnlt"}, {"Name": "label", "Value": "Rapport.html"}, {"Name": "integrity", "Value": "sha256-c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo="}]}, {"Route": "Rapport.pdyi0fsnlt.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Rapport.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10441"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pdyi0fsnlt"}, {"Name": "label", "Value": "Rapport.html"}, {"Name": "integrity", "Value": "sha256-c2WcwXO/NyeaTcgaQA7JnANAsT+qAjCKzviUbCymDxo="}]}, {"Route": "Rapport.pdyi0fsnlt.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\5a6tfv8f1g-pdyi0fsnlt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pdyi0fsnlt"}, {"Name": "label", "Value": "Rapport.html.gz"}, {"Name": "integrity", "Value": "sha256-Hq95AkG1kE0Rjwg1qvFkDPQIc4o/ZyyFwON+/RGMWFU="}]}, {"Route": "Register.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\oz70jm6r81-m6a14prokw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000515729758"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1938"}, {"Name": "ETag", "Value": "\"x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo="}]}, {"Route": "Register.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Register.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6961"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo="}]}, {"Route": "Register.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\oz70jm6r81-m6a14prokw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1938"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE="}]}, {"Route": "Register.m6a14prokw.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\oz70jm6r81-m6a14prokw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000515729758"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1938"}, {"Name": "ETag", "Value": "\"x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m6a14prokw"}, {"Name": "label", "Value": "Register.html"}, {"Name": "integrity", "Value": "sha256-ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo="}]}, {"Route": "Register.m6a14prokw.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Register.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6961"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m6a14prokw"}, {"Name": "label", "Value": "Register.html"}, {"Name": "integrity", "Value": "sha256-ziEyR0y1JoEb8sL1B8FrAalpEtZAU1f8L4l6UW7K3Xo="}]}, {"Route": "Register.m6a14prokw.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\oz70jm6r81-m6a14prokw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1938"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m6a14prokw"}, {"Name": "label", "Value": "Register.html.gz"}, {"Name": "integrity", "Value": "sha256-x2XIgZyAtj4PtCONowQOPRQ8jc+taK+v2154l2EBzhE="}]}, {"Route": "Sites.gs3mxpddy3.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\cfe1nezmlr-gs3mxpddy3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000392927308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2544"}, {"Name": "ETag", "Value": "\"G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gs3mxpddy3"}, {"Name": "label", "Value": "Sites.html"}, {"Name": "integrity", "Value": "sha256-UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU="}]}, {"Route": "Sites.gs3mxpddy3.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Sites.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8279"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gs3mxpddy3"}, {"Name": "label", "Value": "Sites.html"}, {"Name": "integrity", "Value": "sha256-UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU="}]}, {"Route": "Sites.gs3mxpddy3.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\cfe1nezmlr-gs3mxpddy3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2544"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gs3mxpddy3"}, {"Name": "label", "Value": "Sites.html.gz"}, {"Name": "integrity", "Value": "sha256-G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc="}]}, {"Route": "Sites.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\cfe1nezmlr-gs3mxpddy3.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000392927308"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2544"}, {"Name": "ETag", "Value": "\"G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU="}]}, {"Route": "Sites.html", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Sites.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8279"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UG4Ngw7C3JuFfIy5kf9vn/1TUBjKRNB6fo8XKuC+9PU="}]}, {"Route": "Sites.html.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\cfe1nezmlr-gs3mxpddy3.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2544"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 10:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G+yk8fDxi7Gy0f0gtIhvKUm7YWu2O0byUX63BVZVOMc="}]}, {"Route": "Style.css", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4nl7ma3ohr-kjm711ejyc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002320185615"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "ETag", "Value": "\"T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE="}]}, {"Route": "Style.css", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE="}]}, {"Route": "Style.css.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4nl7ma3ohr-kjm711ejyc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs="}]}, {"Route": "Style.kjm711ejyc.css", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4nl7ma3ohr-kjm711ejyc.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002320185615"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "ETag", "Value": "\"T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjm711ejyc"}, {"Name": "label", "Value": "Style.css"}, {"Name": "integrity", "Value": "sha256-IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE="}]}, {"Route": "Style.kjm711ejyc.css", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\wwwroot\\Style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "834"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjm711ejyc"}, {"Name": "label", "Value": "Style.css"}, {"Name": "integrity", "Value": "sha256-IK1CaqOqGcf6Ay/+pkvd6zZskdOIRzHpeEQivG6yaaE="}]}, {"Route": "Style.kjm711ejyc.css.gz", "AssetFile": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\Debug\\net9.0\\compressed\\4nl7ma3ohr-kjm711ejyc.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "430"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs=\""}, {"Name": "Last-Modified", "Value": "Thu, 31 Jul 2025 13:51:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kjm711ejyc"}, {"Name": "label", "Value": "Style.css.gz"}, {"Name": "integrity", "Value": "sha256-T1RbkT7AYtRCCMNveoFtaxMaZaEg2PfoiMAR9ab+EGs="}]}]}