{"version": 3, "targets": {"net9.0": {"Microsoft.AspNetCore.Authorization/9.0.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components/9.0.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.4", "Microsoft.AspNetCore.Components.Analyzers": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.4": {"type": "package", "build": {"buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets": {}}}, "Microsoft.AspNetCore.Components.Forms/9.0.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Components.Web/9.0.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components": "9.0.4", "Microsoft.AspNetCore.Components.Forms": "9.0.4", "Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4", "Microsoft.JSInterop": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.JSInterop/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.JSInterop.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"related": ".xml"}}}, "Syncfusion.Blazor.Buttons/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Core": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Buttons.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Buttons.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Buttons.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Buttons.props": {}}}, "Syncfusion.Blazor.Calendars/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Inputs": "30.1.42", "Syncfusion.Blazor.Lists": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Calendars.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Calendars.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Calendars.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Calendars.props": {}}}, "Syncfusion.Blazor.Core/30.1.42": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.4", "Syncfusion.Licensing": "30.1.42", "System.Text.Json": "9.0.4"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Core.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Core.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Core.props": {}}}, "Syncfusion.Blazor.Data/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Core": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Data.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Data.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Data.props": {}}}, "Syncfusion.Blazor.DropDowns/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42", "Syncfusion.Blazor.Inputs": "30.1.42", "Syncfusion.Blazor.Notifications": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.DropDowns.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.DropDowns.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.DropDowns.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.DropDowns.props": {}}}, "Syncfusion.Blazor.Grid/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Calendars": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42", "Syncfusion.Blazor.DropDowns": "30.1.42", "Syncfusion.Blazor.Inputs": "30.1.42", "Syncfusion.Blazor.Navigations": "30.1.42", "Syncfusion.Blazor.Popups": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42", "Syncfusion.ExcelExport.Net.Core": "30.1.42", "Syncfusion.PdfExport.Net.Core": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Grids.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Grids.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Grid.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Grid.props": {}}}, "Syncfusion.Blazor.Inputs/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42", "Syncfusion.Blazor.Popups": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42", "Syncfusion.Blazor.SplitButtons": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Inputs.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Inputs.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Inputs.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Inputs.props": {}}}, "Syncfusion.Blazor.Lists/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Lists.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Lists.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Lists.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Lists.props": {}}}, "Syncfusion.Blazor.Navigations/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Data": "30.1.42", "Syncfusion.Blazor.DropDowns": "30.1.42", "Syncfusion.Blazor.Inputs": "30.1.42", "Syncfusion.Blazor.Lists": "30.1.42", "Syncfusion.Blazor.Popups": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Navigations.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Navigations.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Navigations.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Navigations.props": {}}}, "Syncfusion.Blazor.Notifications/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Notifications.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Notifications.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Notifications.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Notifications.props": {}}}, "Syncfusion.Blazor.Popups/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Popups.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Popups.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Popups.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Popups.props": {}}}, "Syncfusion.Blazor.Spinner/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Core": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.Spinner.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.Spinner.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.Spinner.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.Spinner.props": {}}}, "Syncfusion.Blazor.SplitButtons/30.1.42": {"type": "package", "dependencies": {"Syncfusion.Blazor.Buttons": "30.1.42", "Syncfusion.Blazor.Core": "30.1.42", "Syncfusion.Blazor.Popups": "30.1.42", "Syncfusion.Blazor.Spinner": "30.1.42"}, "compile": {"lib/net9.0/Syncfusion.Blazor.SplitButtons.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.Blazor.SplitButtons.dll": {"related": ".xml"}}, "build": {"buildTransitive/Syncfusion.Blazor.SplitButtons.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Syncfusion.Blazor.SplitButtons.props": {}}}, "Syncfusion.ExcelExport.Net.Core/30.1.42": {"type": "package", "compile": {"lib/net9.0/Syncfusion.ExcelExport.Net.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.ExcelExport.Net.dll": {"related": ".xml"}}}, "Syncfusion.Licensing/30.1.42": {"type": "package", "compile": {"lib/net9.0/Syncfusion.Licensing.dll": {}}, "runtime": {"lib/net9.0/Syncfusion.Licensing.dll": {}}}, "Syncfusion.PdfExport.Net.Core/30.1.42": {"type": "package", "compile": {"lib/net9.0/Syncfusion.PdfExport.Net.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Syncfusion.PdfExport.Net.dll": {"related": ".xml"}}}, "System.Text.Json/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}}}, "libraries": {"Microsoft.AspNetCore.Authorization/9.0.4": {"sha512": "QIV3Jk/zb5wsBNkxj4eK4Qoja6f0hO2pUhWrG8NOmgnclA7zbQvzLLKaGnva7/PtglS8VS5HusZfrnnO8FTpSA==", "type": "package", "path": "microsoft.aspnetcore.authorization/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Authorization.dll", "lib/net462/Microsoft.AspNetCore.Authorization.xml", "lib/net9.0/Microsoft.AspNetCore.Authorization.dll", "lib/net9.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.9.0.4.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Components/9.0.4": {"sha512": "imeT4r1PLXqI3kzf7b06Tzj5H4od8o/u4ZE5JgyrlvwABSZlBQzWN8AgT/lPLqs2VJVxNwcAolx+vxLMl9Dn+g==", "type": "package", "path": "microsoft.aspnetcore.components/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.dll", "lib/net9.0/Microsoft.AspNetCore.Components.xml", "microsoft.aspnetcore.components.9.0.4.nupkg.sha512", "microsoft.aspnetcore.components.nuspec"]}, "Microsoft.AspNetCore.Components.Analyzers/9.0.4": {"sha512": "fPrKrrfT5o0dJZ4AgbGsXBl4YNTlSrRcpYMgLTsrmYo7aOkZrdWPmsxVsFRyelfmCW9qHH7ZZK/4cTC8HuS7BA==", "type": "package", "path": "microsoft.aspnetcore.components.analyzers/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "analyzers/dotnet/cs/Microsoft.AspNetCore.Components.Analyzers.dll", "build/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "buildTransitive/netstandard2.0/Microsoft.AspNetCore.Components.Analyzers.targets", "microsoft.aspnetcore.components.analyzers.9.0.4.nupkg.sha512", "microsoft.aspnetcore.components.analyzers.nuspec"]}, "Microsoft.AspNetCore.Components.Forms/9.0.4": {"sha512": "FvoNIyz5URP2QonItKbDBzCcdmSXHWfyh0lUAYkNittlLmFAqaIF4TE55h3/SYLI8ISsbAVd44Cax86HBhLu8w==", "type": "package", "path": "microsoft.aspnetcore.components.forms/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll", "lib/net9.0/Microsoft.AspNetCore.Components.Forms.xml", "microsoft.aspnetcore.components.forms.9.0.4.nupkg.sha512", "microsoft.aspnetcore.components.forms.nuspec"]}, "Microsoft.AspNetCore.Components.Web/9.0.4": {"sha512": "X02T/wGvT7vCgIXG0R4ZWQUx5/qMaKuWJklBL3eaa8b1NJHyPlox6IpI4AcmMy7r1hM8GwaxKfm34dv5b7n5VA==", "type": "package", "path": "microsoft.aspnetcore.components.web/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.txt", "lib/net9.0/Microsoft.AspNetCore.Components.Web.dll", "lib/net9.0/Microsoft.AspNetCore.Components.Web.xml", "microsoft.aspnetcore.components.web.9.0.4.nupkg.sha512", "microsoft.aspnetcore.components.web.nuspec"]}, "Microsoft.AspNetCore.Metadata/9.0.4": {"sha512": "3AhnSEW/Zw0E++itsen8YvqT7UoyF7zYD9OZPQmeGpT7YQP4W9GOiwOljfJEC8+4pP8kqgpeRN/p4LkmKLIUlQ==", "type": "package", "path": "microsoft.aspnetcore.metadata/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Metadata.dll", "lib/net462/Microsoft.AspNetCore.Metadata.xml", "lib/net9.0/Microsoft.AspNetCore.Metadata.dll", "lib/net9.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.9.0.4.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.Extensions.DependencyInjection/9.0.4": {"sha512": "f2MTUaS2EQ3lX4325ytPAISZqgBfXmY0WvgD80ji6Z20AoDNiCESxsqo6mFRwHJD/jfVKRw9FsW6+86gNre3ug==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"sha512": "UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"sha512": "0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.4": {"sha512": "fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "type": "package", "path": "microsoft.extensions.options/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.4.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.4": {"sha512": "SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "type": "package", "path": "microsoft.extensions.primitives/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.4.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.JSInterop/9.0.4": {"sha512": "iuzwm79XoRYTRhf7cOHgMgGAE8ZsAcCGp2VsIDAV05qRzceKSE1S7tQp+8GTzG7+IZpdPpvfaJUwSD/pYIog5A==", "type": "package", "path": "microsoft.jsinterop/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.JSInterop.dll", "lib/net9.0/Microsoft.JSInterop.xml", "microsoft.jsinterop.9.0.4.nupkg.sha512", "microsoft.jsinterop.nuspec"]}, "Syncfusion.Blazor.Buttons/30.1.42": {"sha512": "O1HUjjQ0F7zI4lbMSdhw1DRv1KoSZhaU0L8MgDIrN1I24NX7DRAGOkCpa6vJ7uggD4PiTrYPxNcT1kaUFAH+nA==", "type": "package", "path": "syncfusion.blazor.buttons/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Buttons.props", "buildMultiTargeting/Syncfusion.Blazor.Buttons.props", "buildTransitive/Syncfusion.Blazor.Buttons.props", "lib/net8.0/Syncfusion.Blazor.Buttons.dll", "lib/net8.0/Syncfusion.Blazor.Buttons.xml", "lib/net9.0/Syncfusion.Blazor.Buttons.dll", "lib/net9.0/Syncfusion.Blazor.Buttons.xml", "staticwebassets/scripts/sf-floating-action-button.min.js", "staticwebassets/scripts/sf-speeddial.min.js", "syncfusion.blazor.buttons.30.1.42.nupkg.sha512", "syncfusion.blazor.buttons.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Calendars/30.1.42": {"sha512": "sq8ZGQovVK+0gsM59uQFd451sUC0Lw/figjfNEqK8S8xSFDzz3zVkkdje02SnT+aFItA8BVwgDY+/DVdVk/CGQ==", "type": "package", "path": "syncfusion.blazor.calendars/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Calendars.props", "buildMultiTargeting/Syncfusion.Blazor.Calendars.props", "buildTransitive/Syncfusion.Blazor.Calendars.props", "lib/net8.0/Syncfusion.Blazor.Calendars.dll", "lib/net8.0/Syncfusion.Blazor.Calendars.xml", "lib/net9.0/Syncfusion.Blazor.Calendars.dll", "lib/net9.0/Syncfusion.Blazor.Calendars.xml", "staticwebassets/scripts/sf-calendar.min.js", "staticwebassets/scripts/sf-datepicker.min.js", "staticwebassets/scripts/sf-daterangepicker.min.js", "staticwebassets/scripts/sf-timepicker.min.js", "syncfusion.blazor.calendars.30.1.42.nupkg.sha512", "syncfusion.blazor.calendars.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Core/30.1.42": {"sha512": "OCUL7r2B+904tjPsKFKzi5F0qF0nkJu9EgF4JSVwLlAE1AbkjePvyZo4fJavAPwDHiPQKV6hChjWGFYBr4HOqw==", "type": "package", "path": "syncfusion.blazor.core/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Core.props", "buildMultiTargeting/Syncfusion.Blazor.Core.props", "buildTransitive/Syncfusion.Blazor.Core.props", "lib/net8.0/Syncfusion.Blazor.Core.dll", "lib/net8.0/Syncfusion.Blazor.Core.xml", "lib/net9.0/Syncfusion.Blazor.Core.dll", "lib/net9.0/Syncfusion.Blazor.Core.xml", "staticwebassets/scripts/popup.min.js", "staticwebassets/scripts/popupsbase.min.js", "staticwebassets/scripts/sf-svg-export.min.js", "staticwebassets/scripts/svgbase.min.js", "staticwebassets/scripts/syncfusion-blazor-base.min.js", "staticwebassets/scripts/syncfusion-blazor.min.js", "syncfusion.blazor.core.30.1.42.nupkg.sha512", "syncfusion.blazor.core.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Data/30.1.42": {"sha512": "5ZvQq1XqyZgbGFCQeomuwQlkYlYl6q14QVEN+pFYTIXjYhogtVxczL2i3CO2BYTBHnP+h3A9LHQ5cxeDyDNVAA==", "type": "package", "path": "syncfusion.blazor.data/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Data.props", "buildMultiTargeting/Syncfusion.Blazor.Data.props", "buildTransitive/Syncfusion.Blazor.Data.props", "lib/net8.0/Syncfusion.Blazor.Data.dll", "lib/net8.0/Syncfusion.Blazor.Data.xml", "lib/net9.0/Syncfusion.Blazor.Data.dll", "lib/net9.0/Syncfusion.Blazor.Data.xml", "staticwebassets/scripts/data.min.js", "syncfusion.blazor.data.30.1.42.nupkg.sha512", "syncfusion.blazor.data.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.DropDowns/30.1.42": {"sha512": "3dN2N/rYgneYV/U5bljprcamNdeuQIS/7zgzRScMdsTpZmBxVbNyNWTUXWiuRfsZIpBdsrHxG0WDXxyzYepF/Q==", "type": "package", "path": "syncfusion.blazor.dropdowns/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.DropDowns.props", "buildMultiTargeting/Syncfusion.Blazor.DropDowns.props", "buildTransitive/Syncfusion.Blazor.DropDowns.props", "lib/net8.0/Syncfusion.Blazor.DropDowns.dll", "lib/net8.0/Syncfusion.Blazor.DropDowns.xml", "lib/net9.0/Syncfusion.Blazor.DropDowns.dll", "lib/net9.0/Syncfusion.Blazor.DropDowns.xml", "staticwebassets/scripts/sf-dropdownlist.min.js", "staticwebassets/scripts/sf-listbox.min.js", "staticwebassets/scripts/sf-mention.min.js", "staticwebassets/scripts/sf-multiselect.min.js", "staticwebassets/scripts/sortable.min.js", "syncfusion.blazor.dropdowns.30.1.42.nupkg.sha512", "syncfusion.blazor.dropdowns.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Grid/30.1.42": {"sha512": "nWMjeCUtvr6+Z+3sAaKV01z7/lOjkZ+jZvAYqZLIHg3qJuLO2xxw8BSUzHKibHZT7Oab7BvzVyMbQwphnxPq2w==", "type": "package", "path": "syncfusion.blazor.grid/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Grid.props", "buildMultiTargeting/Syncfusion.Blazor.Grid.props", "buildTransitive/Syncfusion.Blazor.Grid.props", "lib/net8.0/Syncfusion.Blazor.Grids.dll", "lib/net8.0/Syncfusion.Blazor.Grids.xml", "lib/net9.0/Syncfusion.Blazor.Grids.dll", "lib/net9.0/Syncfusion.Blazor.Grids.xml", "staticwebassets/scripts/sf-grid.min.js", "syncfusion.blazor.grid.30.1.42.nupkg.sha512", "syncfusion.blazor.grid.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Inputs/30.1.42": {"sha512": "5YLD/7Q+U9k107gnbbrf1riB/Krc6YgLyvFrTQkOHQdtvQLEimAjsVbOv1Yd3iL2dI7hJS/HdxxSuGScHPKG7w==", "type": "package", "path": "syncfusion.blazor.inputs/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Inputs.props", "buildMultiTargeting/Syncfusion.Blazor.Inputs.props", "buildTransitive/Syncfusion.Blazor.Inputs.props", "lib/net8.0/Syncfusion.Blazor.Inputs.dll", "lib/net8.0/Syncfusion.Blazor.Inputs.xml", "lib/net9.0/Syncfusion.Blazor.Inputs.dll", "lib/net9.0/Syncfusion.Blazor.Inputs.xml", "staticwebassets/scripts/sf-colorpicker.min.js", "staticwebassets/scripts/sf-maskedtextbox.min.js", "staticwebassets/scripts/sf-numerictextbox.min.js", "staticwebassets/scripts/sf-otp-input.min.js", "staticwebassets/scripts/sf-rating.min.js", "staticwebassets/scripts/sf-signature.min.js", "staticwebassets/scripts/sf-slider.min.js", "staticwebassets/scripts/sf-speechtotext.min.js", "staticwebassets/scripts/sf-textarea.min.js", "staticwebassets/scripts/sf-textbox.min.js", "staticwebassets/scripts/sf-uploader.min.js", "syncfusion.blazor.inputs.30.1.42.nupkg.sha512", "syncfusion.blazor.inputs.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Lists/30.1.42": {"sha512": "n+d/KbBHXaB7f/dh4UHb6xOURmAQI8Bn1T2z4GTcNXttHcudxtmKIpOD0xKegfzsGXUS9m/e4dNQ13/HT3ZupA==", "type": "package", "path": "syncfusion.blazor.lists/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Lists.props", "buildMultiTargeting/Syncfusion.Blazor.Lists.props", "buildTransitive/Syncfusion.Blazor.Lists.props", "lib/net8.0/Syncfusion.Blazor.Lists.dll", "lib/net8.0/Syncfusion.Blazor.Lists.xml", "lib/net9.0/Syncfusion.Blazor.Lists.dll", "lib/net9.0/Syncfusion.Blazor.Lists.xml", "staticwebassets/scripts/sf-listview.min.js", "syncfusion.blazor.lists.30.1.42.nupkg.sha512", "syncfusion.blazor.lists.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Navigations/30.1.42": {"sha512": "BgURjGgAF0kPfYGHoN6uvHS/CB0RhMUaWTtXO8l/iL3Duuan9R68rN2VhxmRSmJNa7aG34xkw2PcF+i0RP7SRA==", "type": "package", "path": "syncfusion.blazor.navigations/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Navigations.props", "buildMultiTargeting/Syncfusion.Blazor.Navigations.props", "buildTransitive/Syncfusion.Blazor.Navigations.props", "lib/net8.0/Syncfusion.Blazor.Navigations.dll", "lib/net8.0/Syncfusion.Blazor.Navigations.xml", "lib/net9.0/Syncfusion.Blazor.Navigations.dll", "lib/net9.0/Syncfusion.Blazor.Navigations.xml", "staticwebassets/scripts/navigationsbase.min.js", "staticwebassets/scripts/sf-accordion.min.js", "staticwebassets/scripts/sf-breadcrumb.min.js", "staticwebassets/scripts/sf-carousel.min.js", "staticwebassets/scripts/sf-contextmenu.min.js", "staticwebassets/scripts/sf-dropdowntree.min.js", "staticwebassets/scripts/sf-menu.min.js", "staticwebassets/scripts/sf-pager.min.js", "staticwebassets/scripts/sf-sidebar.min.js", "staticwebassets/scripts/sf-stepper.min.js", "staticwebassets/scripts/sf-tab.min.js", "staticwebassets/scripts/sf-toolbar.min.js", "staticwebassets/scripts/sf-treeview.min.js", "syncfusion.blazor.navigations.30.1.42.nupkg.sha512", "syncfusion.blazor.navigations.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Notifications/30.1.42": {"sha512": "eWglrkehhHICAO19g6GCNzixShq+80oqk5y17GJcYHNJ+c/YzGlrRZI9a4ZM8iGldYKEo8HtjKLc2jZyULBpwg==", "type": "package", "path": "syncfusion.blazor.notifications/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Notifications.props", "buildMultiTargeting/Syncfusion.Blazor.Notifications.props", "buildTransitive/Syncfusion.Blazor.Notifications.props", "lib/net8.0/Syncfusion.Blazor.Notifications.dll", "lib/net8.0/Syncfusion.Blazor.Notifications.xml", "lib/net9.0/Syncfusion.Blazor.Notifications.dll", "lib/net9.0/Syncfusion.Blazor.Notifications.xml", "staticwebassets/scripts/sf-toast.min.js", "syncfusion.blazor.notifications.30.1.42.nupkg.sha512", "syncfusion.blazor.notifications.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Popups/30.1.42": {"sha512": "7OvjMizvioR9AwKwBbyqGLeWd++5oOlc47v5+6sne0RQRp8oZgDJy5Sgf7cUkRFXpZcdRhi4DjLPMplS7OPtwg==", "type": "package", "path": "syncfusion.blazor.popups/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Popups.props", "buildMultiTargeting/Syncfusion.Blazor.Popups.props", "buildTransitive/Syncfusion.Blazor.Popups.props", "lib/net8.0/Syncfusion.Blazor.Popups.dll", "lib/net8.0/Syncfusion.Blazor.Popups.xml", "lib/net9.0/Syncfusion.Blazor.Popups.dll", "lib/net9.0/Syncfusion.Blazor.Popups.xml", "staticwebassets/scripts/sf-dialog.min.js", "staticwebassets/scripts/sf-tooltip.min.js", "syncfusion.blazor.popups.30.1.42.nupkg.sha512", "syncfusion.blazor.popups.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.Spinner/30.1.42": {"sha512": "gezwhFfER1tnszBE644mohK09tLJnbjED4nBvLkRsR/UYxhyTu1lhLVAmPxNwxfHbDguH4rsgMk8YcsYHtPK/A==", "type": "package", "path": "syncfusion.blazor.spinner/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.Spinner.props", "buildMultiTargeting/Syncfusion.Blazor.Spinner.props", "buildTransitive/Syncfusion.Blazor.Spinner.props", "lib/net8.0/Syncfusion.Blazor.Spinner.dll", "lib/net8.0/Syncfusion.Blazor.Spinner.xml", "lib/net9.0/Syncfusion.Blazor.Spinner.dll", "lib/net9.0/Syncfusion.Blazor.Spinner.xml", "staticwebassets/scripts/sf-spinner.min.js", "staticwebassets/scripts/spinner.min.js", "syncfusion.blazor.spinner.30.1.42.nupkg.sha512", "syncfusion.blazor.spinner.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Blazor.SplitButtons/30.1.42": {"sha512": "72HehyaOii+NtkBNaK+xYYsofpBebFYQHW2q6Ub7F4+NdCJZEFsjZXZzd2UXSphd3MDH0RZKMeBrgz0lAb071A==", "type": "package", "path": "syncfusion.blazor.splitbuttons/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "build/Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "build/Microsoft.AspNetCore.StaticWebAssets.props", "build/Syncfusion.Blazor.SplitButtons.props", "buildMultiTargeting/Syncfusion.Blazor.SplitButtons.props", "buildTransitive/Syncfusion.Blazor.SplitButtons.props", "lib/net8.0/Syncfusion.Blazor.SplitButtons.dll", "lib/net8.0/Syncfusion.Blazor.SplitButtons.xml", "lib/net9.0/Syncfusion.Blazor.SplitButtons.dll", "lib/net9.0/Syncfusion.Blazor.SplitButtons.xml", "staticwebassets/scripts/sf-drop-down-button.min.js", "staticwebassets/scripts/splitbuttonsbase.min.js", "syncfusion.blazor.splitbuttons.30.1.42.nupkg.sha512", "syncfusion.blazor.splitbuttons.nuspec", "syncfusion_logo.png"]}, "Syncfusion.ExcelExport.Net.Core/30.1.42": {"sha512": "dPETqUtSeP2+gpMdVtCwlh284fqX6sy6GRBDoh6Cob+QZv3PV5bNq2NbUAwnBPpV+jhynspagczEn6x72xrNGQ==", "type": "package", "path": "syncfusion.excelexport.net.core/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net8.0/Syncfusion.ExcelExport.Net.dll", "lib/net8.0/Syncfusion.ExcelExport.Net.xml", "lib/net9.0/Syncfusion.ExcelExport.Net.dll", "lib/net9.0/Syncfusion.ExcelExport.Net.xml", "lib/netstandard2.0/Syncfusion.ExcelExport.Net.dll", "lib/netstandard2.0/Syncfusion.ExcelExport.Net.xml", "syncfusion.excelexport.net.core.30.1.42.nupkg.sha512", "syncfusion.excelexport.net.core.nuspec", "syncfusion_logo.png"]}, "Syncfusion.Licensing/30.1.42": {"sha512": "ykLfYHAplHfljY5Iq1P5hzeF+dgGnfU/BfmU/z7rvk6SszSTM/Lkub1PO1eqpZdXhx+symcxbHHe5j7P7HyfBA==", "type": "package", "path": "syncfusion.licensing/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "lib/MonoAndroid90/Syncfusion.Licensing.dll", "lib/Xamarin.Mac/Syncfusion.Licensing.dll", "lib/Xamarin.iOS10/Syncfusion.Licensing.dll", "lib/net462/Syncfusion.Licensing.dll", "lib/net8.0/Syncfusion.Licensing.dll", "lib/net9.0/Syncfusion.Licensing.dll", "lib/netstandard2.0/Syncfusion.Licensing.dll", "lib/uap10.0/Syncfusion.Licensing.dll", "syncfusion.licensing.30.1.42.nupkg.sha512", "syncfusion.licensing.nuspec", "syncfusion_logo.png"]}, "Syncfusion.PdfExport.Net.Core/30.1.42": {"sha512": "h3vX39eSEGJzBjZLDVp0h1vdhh/4L2f7W38gDMeORmROTWsroagVvYtsJPZsVHcPTPrs7HkZQlKXDbCWS6gr8Q==", "type": "package", "path": "syncfusion.pdfexport.net.core/30.1.42", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net8.0/Syncfusion.PdfExport.Net.dll", "lib/net8.0/Syncfusion.PdfExport.Net.xml", "lib/net9.0/Syncfusion.PdfExport.Net.dll", "lib/net9.0/Syncfusion.PdfExport.Net.xml", "lib/netstandard2.0/Syncfusion.PdfExport.Net.dll", "lib/netstandard2.0/Syncfusion.PdfExport.Net.xml", "syncfusion.pdfexport.net.core.30.1.42.nupkg.sha512", "syncfusion.pdfexport.net.core.nuspec", "syncfusion_logo.png"]}, "System.Text.Json/9.0.4": {"sha512": "pYtmpcO6R3Ef1XilZEHgXP2xBPVORbYEzRP7dl0IAAbN8Dm+kfwio8aCKle97rAWXOExr292MuxWYurIuwN62g==", "type": "package", "path": "system.text.json/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.4.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["Syncfusion.Blazor.Grid >= 30.1.42"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\VTLeociaWebApp.csproj", "projectName": "VTLeociaWebApp", "projectPath": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\VTLeociaWebApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Syncfusion.Blazor.Grid": {"target": "Package", "version": "[30.1.42, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}