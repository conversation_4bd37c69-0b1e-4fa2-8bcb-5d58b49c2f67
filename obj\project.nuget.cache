{"version": 2, "dgSpecHash": "8ymz2RrWquo=", "success": true, "projectFilePath": "C:\\SVN_VT\\VTLeociaWebApp\\VTLeociaWebApp\\VTLeociaWebApp.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\9.0.4\\microsoft.aspnetcore.authorization.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\9.0.4\\microsoft.aspnetcore.components.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\9.0.4\\microsoft.aspnetcore.components.analyzers.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\9.0.4\\microsoft.aspnetcore.components.forms.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\9.0.4\\microsoft.aspnetcore.components.web.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\9.0.4\\microsoft.aspnetcore.metadata.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.4\\microsoft.extensions.dependencyinjection.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.4\\microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.4\\microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.4\\microsoft.extensions.options.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.4\\microsoft.extensions.primitives.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\9.0.4\\microsoft.jsinterop.9.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.buttons\\30.1.42\\syncfusion.blazor.buttons.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.calendars\\30.1.42\\syncfusion.blazor.calendars.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.core\\30.1.42\\syncfusion.blazor.core.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.data\\30.1.42\\syncfusion.blazor.data.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.dropdowns\\30.1.42\\syncfusion.blazor.dropdowns.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.grid\\30.1.42\\syncfusion.blazor.grid.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.inputs\\30.1.42\\syncfusion.blazor.inputs.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.lists\\30.1.42\\syncfusion.blazor.lists.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.navigations\\30.1.42\\syncfusion.blazor.navigations.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.notifications\\30.1.42\\syncfusion.blazor.notifications.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.popups\\30.1.42\\syncfusion.blazor.popups.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.spinner\\30.1.42\\syncfusion.blazor.spinner.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.splitbuttons\\30.1.42\\syncfusion.blazor.splitbuttons.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.excelexport.net.core\\30.1.42\\syncfusion.excelexport.net.core.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.licensing\\30.1.42\\syncfusion.licensing.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.pdfexport.net.core\\30.1.42\\syncfusion.pdfexport.net.core.30.1.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.4\\system.text.json.9.0.4.nupkg.sha512"], "logs": []}