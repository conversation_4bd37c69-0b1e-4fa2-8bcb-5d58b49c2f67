<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Accueil - Logiciel d'Interventions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow-x: hidden;
        }

        /* ✅ Particules en arrière-plan avec fond dégradé */
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 0;
            background: linear-gradient( to right, rgba(11, 61, 145, 0.9), rgba(135, 206, 235, 0.6) );
        }

        /* ✅ Animation texte au chargement */
        @keyframes fadeSlideIn {
            0% {
                opacity: 0;
                transform: translateX(-30px);
            }

            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .text-content {
            animation: fadeSlideIn 1s ease forwards;
        }

        /* ✅ Animation hover boutons */
        .btn {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

            .btn:hover {
                transform: scale(1.05);
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            }

        /* ✅ Animation oscillation douce image */
        @keyframes swing {
            0%, 100% {
                transform: rotate(0deg);
            }

            50% {
                transform: rotate(3deg);
            }
        }

        .image-content img {
            animation: swing 4s ease-in-out infinite;
            transform-origin: center bottom;
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">


</head>
<body>
    <!-- 🌌 Fond + particules -->
    <div id="particles-js"></div>

    <!-- 🏠 Hero Section -->
    <!-- 🌗 Bouton Dark/Light -->
    <button id="toggle-theme" class="btn btn-sm btn-light position-absolute top-0 end-0 m-3 d-flex align-items-center justify-content-center"
            style="z-index: 9999;">
        <i id="theme-icon" class="bi bi-moon-fill fs-5"></i>
    </button>


    <section class="hero-section d-flex align-items-center text-white"
             style="position: relative; z-index: 1; height: 100vh;">
        <div class="container d-flex justify-content-between align-items-center" style="height: 100%;">
            <div class="text-content" style="max-width: 50%;">
                <h1 class="display-4 fw-bold mb-3">Simplifiez la gestion de vos interventions techniques</h1>
                <p class="lead mb-4">Planifiez, suivez et optimisez toutes vos missions grâce à une plateforme moderne et intuitive.</p>
                <a href="Login.html" class="btn btn-lg btn-primary me-3 px-4 py-2">Connexion</a>
                <a href="Register.html" class="btn btn-lg btn-outline-light px-4 py-2">Créer un compte</a>
            </div>
            <div class="image-content" style="max-width: 45%;">
                <img src="images/ChatGPT Image 22 juil. 2025, 13_51_40.png" alt="Image idée" style="width: 100%; height: auto; border-radius: 10px;">
            </div>
        </div>
    </section>

    <!-- ✅ Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

    <script>
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 50,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#ffffff"
                },
                "shape": {
                    "type": "circle"
                },
                "opacity": {
                    "value": 0.5,
                    "random": true
                },
                "size": {
                    "value": 3,
                    "random": true
                },
                "move": {
                    "enable": true,
                    "speed": 2,
                    "direction": "none",
                    "random": true,
                    "straight": false,
                    "out_mode": "out"
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "repulse"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    }
                },
                "modes": {
                    "repulse": {
                        "distance": 100,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    }
                }
            },
            "retina_detect": true
        });
    </script>
    <script>
        const toggleBtn = document.getElementById('toggle-theme');
        const icon = document.getElementById('theme-icon');

        toggleBtn.addEventListener('click', () => {
            document.body.classList.toggle('dark-mode');
            if (document.body.classList.contains('dark-mode')) {
                icon.classList.replace('bi-moon-fill', 'bi-sun-fill');
            } else {
                icon.classList.replace('bi-sun-fill', 'bi-moon-fill');
            }
        });
    </script>

    <style>
        #toggle-theme {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            padding: 0;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            cursor: pointer;
            z-index: 9999;
        }

        /* Thème sombre */
        body.dark-mode {
            background-color: #121212;
            color: #ffffff;
        }

            body.dark-mode .hero-section {
                background: linear-gradient(to right, rgba(30, 30, 30, 0.95), rgba(50, 50, 50, 0.6)) !important;
            }

            body.dark-mode .btn-primary {
                background-color: #0d6efd;
                border-color: #0d6efd;
            }

            body.dark-mode .btn-outline-light {
                color: #ffffff;
                border-color: #ffffff;
            }

            body.dark-mode #toggle-theme {
                background-color: #ffffff;
                color: #000000;
            }

        /* Pricing plans */
        .pricing-plans {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            margin-bottom: 3rem;
            justify-content: center;
        }

        .plan-card {
            flex: 1 1 280px;
            background: white;
            border-radius: 12px;
            padding: 2rem 1.8rem;
            box-shadow: 0 6px 20px rgb(0 0 0 / 0.1);
            transition: box-shadow 0.3s ease;
            text-align: center;
            cursor: pointer;
        }

            .plan-card:hover {
                box-shadow: 0 12px 30px rgb(0 0 0 / 0.15);
            }

        .plan-title {
            font-weight: 700;
            font-size: 1.3rem;
            color: #0b3d91;
            margin-bottom: 0.5rem;
        }

        .plan-price {
            font-size: 2rem;
            font-weight: 700;
            color: #0d6efd;
            margin-bottom: 1rem;
        }

        .plan-desc {
            font-size: 0.9rem;
            color: #555;
            margin-bottom: 1.5rem;
            min-height: 60px;
        }

        .plan-btn {
            background-color: #0d6efd;
            border: none;
            border-radius: 8px;
            padding: 10px 25px;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            transition: background-color 0.3s ease;
            width: 100%;
            cursor: pointer;
        }

            .plan-btn:hover {
                background-color: #0b5ed7;
            }

        /* Why choose us */
        /* Styles pour "Why Choose Us" amélioré */
        .section-title {
            color: #0b3d91;
        }

        .icon-box {
            background: #fff;
            border-radius: 12px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

            .icon-box:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
            }

            .icon-box i {
                color: #0d6efd;
            }

            .icon-box h5 {
                color: #0b3d91;
            }

            .icon-box p {
                color: #555;
                font-size: 0.95rem;
            }

        /* FAQ */
        /* FAQ Accordion Styling */
        .accordion-button {
            background-color: #f8f9fa;
            color: #333;
            border: none;
            padding: 1rem 1.25rem;
            font-size: 1.05rem;
            transition: background-color 0.3s ease, color 0.3s ease;
            box-shadow: none;
        }

            .accordion-button:not(.collapsed) {
                background-color: #0d6efd;
                color: #fff;
                box-shadow: inset 0 -1px 0 rgba(0,0,0,0.125);
            }

            .accordion-button::after {
                transform: rotate(-90deg);
                transition: transform 0.3s ease;
            }

            .accordion-button:not(.collapsed)::after {
                transform: rotate(0deg);
            }

        .accordion-body {
            background-color: #fff;
            border-top: 1px solid #dee2e6;
            padding: 1rem 1.25rem;
            font-size: 0.95rem;
            line-height: 1.6;
            color: #444;
        }

        .accordion-item {
            transition: box-shadow 0.3s ease;
        }

            .accordion-item:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }

        .section-title {
            font-size: 2rem;
            color: #0d6efd;
            font-weight: bold;
        }

        @media (max-width: 576px) {
            .accordion-button {
                font-size: 1rem;
            }

            .section-title {
                font-size: 1.5rem;
            }
        }


        /* Footer */
        footer {
            text-align: center;
            padding: 1.5rem 0;
            color: #777;
            font-size: 0.9rem;
            border-top: 1px solid #ddd;
            margin-top: 3rem;
        }

        .contact-section {
            background-color: #ffffff;
            border-radius: 16px;
            padding: 3rem 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
            margin-bottom: 4rem;
        }

        .contact-container {
            max-width: 1100px;
            margin: 0 auto;
            align-items: flex-start;
        }

        .contact-form {
            flex: 1 1 400px;
            background: #f9f9f9;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

            .contact-form .form-group {
                margin-bottom: 1.5rem;
            }

            .contact-form label {
                font-weight: 600;
                margin-bottom: 0.5rem;
                display: block;
                color: #333;
            }

            .contact-form input,
            .contact-form textarea {
                width: 100%;
                padding: 0.75rem 1rem;
                border: 1px solid #ccc;
                border-radius: 8px;
                font-size: 0.95rem;
                transition: border-color 0.3s;
            }

                .contact-form input:focus,
                .contact-form textarea:focus {
                    border-color: #0d6efd;
                    outline: none;
                }

        .btn-send {
            background-color: #0d6efd;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
            width: 100%;
        }

            .btn-send:hover {
                background-color: #084dbf;
            }

        .contact-info {
            flex: 1 1 300px;
            padding: 1rem 2rem;
            color: #444;
            font-size: 0.95rem;
        }

            .contact-info h4 {
                font-size: 1.3rem;
                font-weight: 700;
                margin-bottom: 1rem;
                color: #0b3d91;
            }
    </style>
    <main style="position: relative; z-index: 1; background-color: #f8f9fa; padding: 4rem 2rem;">
        <!-- Pricing plans -->
        <section>
            <h2 class="section-title text-center mb-5">Nos forfaits tarifaires</h2>
            <div class="pricing-plans d-flex justify-content-center flex-wrap gap-4">
                <div class="plan-card">
                    <div class="plan-title">Basique</div>
                    <div class="plan-price">199 MAD / mois</div>
                    <div class="plan-desc">Pour les petites équipes débutantes.</div>
                    <button class="plan-btn">Choisir ce forfait</button>
                </div>
                <div class="plan-card">
                    <div class="plan-title">Professionnel</div>
                    <div class="plan-price">499 MAD / mois</div>
                    <div class="plan-desc">Idéal pour les entreprises en pleine croissance.</div>
                    <button class="plan-btn">Choisir ce forfait</button>
                </div>
                <div class="plan-card">
                    <div class="plan-title">Entreprise</div>
                    <div class="plan-price">999 MAD / mois</div>
                    <div class="plan-desc">Fonctionnalités avancées et support dédié.</div>
                    <button class="plan-btn">Choisir ce forfait</button>
                </div>
            </div>
        </section>

        <!-- Why Choose Us -->
        <!-- Why Choose Us -->
        <section class="py-5">
            <div class="container">
                <h2 class="section-title text-center mb-5 fw-bold">Pourquoi choisir TechInterv ?</h2>
                <div class="row g-4">
                    <div class="col-md-4 text-center">
                        <div class="icon-box p-4 shadow-sm">
                            <i class="bi bi-clock fs-1 mb-3 text-primary"></i>
                            <h5 class="fw-semibold mb-2">Réactivité 24/7</h5>
                            <p>Une assistance ultra‑réactive pour vos urgences et besoins quotidiens.</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="icon-box p-4 shadow-sm">
                            <i class="bi bi-shield-lock fs-1 mb-3 text-primary"></i>
                            <h5 class="fw-semibold mb-2">Sécurité renforcée</h5>
                            <p>Toutes vos données sont hébergées de manière chiffrée et protégées en continu.</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="icon-box p-4 shadow-sm">
                            <i class="bi bi-graph-up-arrow fs-1 mb-3 text-primary"></i>
                            <h5 class="fw-semibold mb-2">Statistiques claires</h5>
                            <p>Rapports visuels précis pour piloter vos décisions métier efficacement.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
<style>
    .transition-hover {
        transition: all 0.3s ease-in-out;
        cursor: default;
    }

        .transition-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
            background-color: #f8f9fa;
        }
</style>

        <!-- ❓ FAQ Professionnelle -->
        <section class="py-5 bg-light">
            <div class="container">
                <h2 class="section-title text-center mb-5 fw-bold">Foire aux Questions</h2>
                <div class="accordion" id="faqAccordion">
                    <!-- Q1 -->
                    <div class="accordion-item border-0 shadow-sm mb-3 rounded">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                💡 Comment ajouter un client ?
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Accédez à la section <strong>"Clients"</strong>, puis cliquez sur <em>"Ajouter un client"</em>. Remplissez les informations et validez.
                            </div>
                        </div>
                    </div>

                    <!-- Q2 -->
                    <div class="accordion-item border-0 shadow-sm mb-3 rounded">
                        <h2 class="accordion-header" id="headingTwo">
                            <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                🔐 Mes données sont-elles sécurisées ?
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Oui, toutes les données sont <strong>chiffrées</strong> et stockées sur des serveurs sécurisés en conformité avec les normes européennes.
                            </div>
                        </div>
                    </div>

                    <!-- Q3 -->
                    <div class="accordion-item border-0 shadow-sm mb-3 rounded">
                        <h2 class="accordion-header" id="headingThree">
                            <button class="accordion-button collapsed fw-semibold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                📊 Puis-je exporter les rapports ?
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Oui, dans la section <strong>"Rapports"</strong>, vous pouvez exporter vos données en PDF ou Excel avec un simple clic.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contactez-nous -->
        <section id="contact" class="contact-section mt-5">

            <h2 class="section-title text-center mb-4">Contactez-nous</h2>
            <div class="contact-container d-flex flex-wrap gap-4 justify-content-center">
                <!-- Formulaire -->
                <form class="contact-form">
                    <div class="form-group">
                        <label for="name">Nom complet</label>
                        <input type="text" id="name" placeholder="Entrez votre nom" required />
                    </div>
                    <div class="form-group">
                        <label for="email">Adresse e-mail</label>
                        <input type="email" id="email" placeholder="<EMAIL>" required />
                    </div>
                    <div class="form-group">
                        <label for="message">Votre message</label>
                        <textarea id="message" rows="5" placeholder="Écrivez votre message ici..." required></textarea>
                    </div>
                    <button type="submit" class="btn-send">Envoyer le message</button>
                </form>

                <!-- Informations de contact -->
                <div class="contact-info">
                    <h4>Informations de contact</h4>
                    <p><strong>📍 Adresse :</strong> 123 Rue Technologique, Casablanca, Maroc</p>
                    <p><strong>📞 Téléphone :</strong> +212 6 00 00 00 00</p>
                    <p><strong>✉️ Email :</strong> <EMAIL></p>
                    <p><strong>🕒 Disponibilité :</strong> Lundi - Vendredi : 09h à 18h</p>
                </div>
            </div>
        </section>


        <footer class="text-center mt-5">
            &copy; 2025 MonDashboard - Logiciel de gestion technique
        </footer>
    </main>
    
    <script>
        document.querySelectorAll('.faq-item').forEach(item => {
            item.addEventListener('click', () => {
                item.classList.toggle('open');
            });
        });
    </script>

</body>
</html>
