
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>📄 Rapports</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        #sidebar {
            width: 260px;
            background-color: #0d6efd; /* bleu bootstrap standard, plus clair */
            color: white;
            height: 100vh; /* pleine hauteur */
            position: fixed;
            padding-top: 2rem;
            box-shadow: 3px 0 10px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            user-select: none;
            overflow-y: auto; /* scroll si sidebar trop longue */
        }



            #sidebar .nav-link {
                color: #cbd5f0; /* bleu clair */
                font-weight: 600;
                padding: 15px 25px;
                border-left: 4px solid transparent;
                transition: background-color 0.3s, border-color 0.3s;
                cursor: pointer;
            }

                #sidebar .nav-link:hover {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

                #sidebar .nav-link.active {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

            #sidebar .submenu {
                padding-left: 1.5rem;
                font-weight: 500;
                display: none;
                flex-direction: column;
            }

                #sidebar .submenu .nav-link {
                    padding: 10px 25px;
                    font-size: 0.9rem;
                    color: #a8bbea;
                    border-left: none;
                }

                    #sidebar .submenu .nav-link:hover {
                        color: #d0e1ff;
                        background-color: transparent;
                    }
    </style>
</head>
<body class="bg-light">
    <!-- Sidebar -->
    <nav id="sidebar" class="d-flex flex-column">
        <!-- En-tête de la sidebar -->
        <div class="text-center mb-4">
            <img src="images/Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white; margin-bottom: 10px;">
            <h4 style="margin: 0; font-weight: 700;">TechInterv</h4>
            <small>Logiciel d'interventions</small>
        </div>

        <a href="Dashboard.html" class="nav-link active">Accueil</a>
        <a href="Clients.html" class="nav-link">Clients</a>
        <a href="Gestion de stock.html" class="nav-link">Gestion de stock</a>
        <a href="Mission.html" class="nav-link">Mission</a>
        <a href="MonCompte.html" class="nav-link">Mon compte</a>

        <!-- Sous-menu réduit uniquement à "Déconnexion" -->
        <div class="submenu">
            <a href="login.html" class="nav-link text-danger">
                <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
            </a>
        </div>
        <a href="Sites.html" class="nav-link">Sites</a>
        <a href="Rapport.html" class="nav-link">Rapport</a>

        <a href="Login.html" onclick="logout()" class="nav-link logout-link mt-auto d-flex align-items-center gap-2">
            <i class="bi bi-box-arrow-right"></i>
            <span>Se d&#233;connecter</span>
        </a>

    </nav>
    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary">📄 Rapports</h2>
            <button class="btn btn-success">📤 Exporter tout</button>
        </div>

        <!-- Filtres -->
        <div class="card mb-4 p-3 shadow-sm">
            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">📍 Site</label>
                    <select class="form-select">
                        <option>Tous les sites</option>
                        <option>Usine S3</option>
                        <option>Hôtel Central</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">📅 Période</label>
                    <input type="month" class="form-control">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button class="btn btn-primary w-100">🔍 Filtrer</button>
                </div>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card p-3 shadow-sm">
                    <h6 class="text-muted">Rapports générés</h6>
                    <h4>87</h4>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card p-3 shadow-sm">
                    <h6 class="text-muted">Interventions validées</h6>
                    <h4>65</h4>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card p-3 shadow-sm">
                    <h6 class="text-muted">Rapports en attente</h6>
                    <h4>12</h4>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card p-3 shadow-sm">
                    <h6 class="text-muted">Documents exportés</h6>
                    <h4>24</h4>
                </div>
            </div>
        </div>

        <!-- Liste des rapports -->
        <div class="card shadow-sm">
            <div class="card-body">
                <h5 class="card-title mb-4">🧾 Liste des rapports</h5>
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Site</th>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>001</td>
                            <td>Usine S3</td>
                            <td>2025-07-28</td>
                            <td>Maintenance</td>
                            <td><span class="badge bg-success">Validé</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Voir</button>
                                <button class="btn btn-sm btn-outline-secondary">📥 PDF</button>
                            </td>
                        </tr>
                        <tr>
                            <td>002</td>
                            <td>Hôtel Central</td>
                            <td>2025-07-25</td>
                            <td>Inspection</td>
                            <td><span class="badge bg-warning text-dark">En attente</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Voir</button>
                            </td>
                        </tr>
                        <tr>
                            <td>003</td>
                            <td>Résidence Atlas</td>
                            <td>2025-07-20</td>
                            <td>Électricité</td>
                            <td><span class="badge bg-success">Validé</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Voir</button>
                                <button class="btn btn-sm btn-outline-secondary">📥 PDF</button>
                            </td>
                        </tr>
                        <tr>
                            <td>004</td>
                            <td>Clinique Santé Plus</td>
                            <td>2025-07-18</td>
                            <td>Maintenance</td>
                            <td><span class="badge bg-danger">Rejeté</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Voir</button>
                            </td>
                        </tr>
                        <tr>
                            <td>005</td>
                            <td>Usine Textile – Fès</td>
                            <td>2025-07-15</td>
                            <td>Inspection</td>
                            <td><span class="badge bg-warning text-dark">En attente</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Voir</button>
                            </td>
                        </tr>
                        <tr>
                            <td>006</td>
                            <td>Agence Marrakech Centre</td>
                            <td>2025-07-12</td>
                            <td>Audit sécurité</td>
                            <td><span class="badge bg-success">Validé</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Voir</button>
                                <button class="btn btn-sm btn-outline-secondary">📥 PDF</button>
                            </td>
                        </tr>
                        <tr>
                            <td>007</td>
                            <td>Centre Logistique Casablanca</td>
                            <td>2025-07-09</td>
                            <td>Contrôle HVAC</td>
                            <td><span class="badge bg-warning text-dark">En attente</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">Voir</button>
                            </td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

    </div>
</body>
</html>
