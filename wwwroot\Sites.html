<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>📍 Sites</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }

        .map-placeholder {
            height: 400px;
            background-color: #e3eaf2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            color: #5a5a5a;
            border: 2px dashed #a3c0de;
        }

        .site-list .list-group-item {
            cursor: pointer;
            transition: background-color 0.2s;
        }

            .site-list .list-group-item:hover {
                background-color: #f0f8ff;
            }

        .site-list .active {
            background-color: #0d6efd !important;
            color: white;
        }

        .search-input {
            border-radius: 30px;
            padding-left: 1rem;
        }

        .btn-add {
            border-radius: 30px;
        }
        #sidebar {
            width: 260px;
            background-color: #0d6efd; /* bleu bootstrap standard, plus clair */
            color: white;
            height: 100vh; /* pleine hauteur */
            position: fixed;
            padding-top: 2rem;
            box-shadow: 3px 0 10px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            user-select: none;
            overflow-y: auto; /* scroll si sidebar trop longue */
        }



            #sidebar .nav-link {
                color: #cbd5f0; /* bleu clair */
                font-weight: 600;
                padding: 15px 25px;
                border-left: 4px solid transparent;
                transition: background-color 0.3s, border-color 0.3s;
                cursor: pointer;
            }

                #sidebar .nav-link:hover {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

                #sidebar .nav-link.active {
                    background-color: #0d4fbd;
                    color: white;
                    border-left-color: #0d6efd;
                }

            #sidebar .submenu {
                padding-left: 1.5rem;
                font-weight: 500;
                display: none;
                flex-direction: column;
            }

                #sidebar .submenu .nav-link {
                    padding: 10px 25px;
                    font-size: 0.9rem;
                    color: #a8bbea;
                    border-left: none;
                }

                    #sidebar .submenu .nav-link:hover {
                        color: #d0e1ff;
                        background-color: transparent;
                    }

    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav id="sidebar" class="d-flex flex-column">
        <!-- En-tête de la sidebar -->
        <div class="text-center mb-4">
            <img src="images\Profile.png" alt="Logo" style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; border: 2px solid white; margin-bottom: 10px;">
            <h4 style="margin: 0; font-weight: 700;">TechInterv</h4>
            <small>Logiciel d'interventions</small>
        </div>

        <a href="Dashboard.html" class="nav-link active">Accueil</a>
        <a href="Clients.html" class="nav-link">Clients</a>
        <a href="Gestion de stock.html" class="nav-link">Gestion de stock</a>
        <a href="Mission.html" class="nav-link">Mission</a>
        <a href="MonCompte.html" class="nav-link">Mon compte</a>

        <!-- Sous-menu réduit uniquement à "Déconnexion" -->
        <div class="submenu">
            <a href="login.html" class="nav-link text-danger">
                <i class="bi bi-box-arrow-right me-2"></i> Déconnexion
            </a>
        </div>
        <a href="Sites.html" class="nav-link">Sites</a>
        <a href="Rapport.html" class="nav-link">Rapport</a>

        <a href="Login.html" onclick="logout()" class="nav-link logout-link mt-auto d-flex align-items-center gap-2">
            <i class="bi bi-box-arrow-right"></i>
            <span>Se d&#233;connecter</span>
        </a>

    </nav>
    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary mb-0">📍 Sites enregistrés</h2>
            <button class="btn btn-success btn-add">
                ➕ Ajouter un site
            </button>
        </div>

        <div class="row">
            <!-- Colonne de gauche : Liste + recherche -->
            <div class="col-md-4 mb-4">
                <input type="text" class="form-control mb-3 search-input" placeholder="🔍 Rechercher un site..." id="siteSearch">

                <div class="card site-list shadow-sm">
                    <ul class="list-group list-group-flush" id="siteList">
                        <li class="list-group-item active">Usine S3 - Zone Est</li>
                        <li class="list-group-item">Hôtel Central</li>
                        <li class="list-group-item">Résidence Atlas</li>
                        <li class="list-group-item">Bureau Administratif</li>
                        <li class="list-group-item">Clinique Santé Plus</li>
                        <li class="list-group-item">Centre Logistique Casablanca</li>
                        <li class="list-group-item">Dépôt Sud - Agadir</li>
                        <li class="list-group-item">Agence Marrakech Centre</li>
                        <li class="list-group-item">Site Solaire – Ouarzazate</li>
                        <li class="list-group-item">Usine Textile – Fès</li>
                        <li class="list-group-item">Station de pompage - Rabat</li>
                    </ul>
                </div>
            </div>

            <!-- Colonne de droite : Carte -->
            <div class="col-md-8 mb-4">
                <div class="card shadow-sm p-3">
                    <h5 class="mb-3">📍 Localisation du site</h5>
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18..."
                            width="100%"
                            height="500"
                            style="border: 0; border-radius: 8px;"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                </div>
            </div>
        </div>

        <!-- Détails du site -->
        <div class="row">
            <div class="col-12">
                <div class="card p-3 shadow-sm">
                    <h5>Détails du site sélectionné</h5>
                    <p class="mb-0 text-muted">Sélectionnez un site dans la liste pour afficher les détails ici.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Filtrage des sites
        document.getElementById('siteSearch').addEventListener('input', function () {
            const filter = this.value.toLowerCase();
            const listItems = document.querySelectorAll('#siteList .list-group-item');

            listItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                item.style.display = text.includes(filter) ? '' : 'none';
            });
        });

        // Activation du site sélectionné
        const siteItems = document.querySelectorAll('#siteList .list-group-item');
        siteItems.forEach(item => {
            item.addEventListener('click', function () {
                siteItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                // Tu pourras ici ajouter un changement de carte selon le site si tu veux plus tard
            });
        });
    </script>
</body>

</html>
