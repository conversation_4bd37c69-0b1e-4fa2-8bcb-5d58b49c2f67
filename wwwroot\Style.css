/* CSS Compatibility fixes */
* {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

/* Fix text-align compatibility */
.text-center, .text-start, .text-end {
    text-align: center;
    text-align: -webkit-match-parent;
    text-align: match-parent;
}

body {
    font-family: 'Segoe UI', sans-serif;
}

.card {
    border-radius: 12px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

/* Blazor error UI improvements */
#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}
